// Table component types

export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean | 'custom'
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  showTooltip?: boolean
  formatter?: (value: any, row: any) => string
  hidden?: boolean
  mobileVisible?: boolean
  resizable?: boolean
  className?: string
  headerClassName?: string
}

export interface TableSort {
  prop: string
  order: 'ascending' | 'descending' | null
}

export interface TableSelection {
  row: any
  index: number
}

export interface TablePagination {
  currentPage: number
  pageSize: number
  total: number
  pageSizes: number[]
  layout: string
}

export interface TableConfig {
  stripe?: boolean
  border?: boolean
  size?: 'large' | 'default' | 'small'
  fit?: boolean
  showHeader?: boolean
  highlightCurrentRow?: boolean
  rowClassName?: string | ((row: any, index: number) => string)
  rowStyle?: object | ((row: any, index: number) => object)
  cellClassName?: string | ((row: any, column: TableColumn, rowIndex: number, columnIndex: number) => string)
  cellStyle?: object | ((row: any, column: TableColumn, rowIndex: number, columnIndex: number) => object)
  headerRowClassName?: string | ((row: any, index: number) => string)
  headerRowStyle?: object | ((row: any, index: number) => object)
  headerCellClassName?: string | ((row: any, column: TableColumn, rowIndex: number, columnIndex: number) => string)
  headerCellStyle?: object | ((row: any, column: TableColumn, rowIndex: number, columnIndex: number) => object)
  rowKey?: string | ((row: any) => string)
  emptyText?: string
  defaultExpandAll?: boolean
  expandRowKeys?: string[]
  defaultSort?: TableSort
  tooltipEffect?: 'dark' | 'light'
  showSummary?: boolean
  sumText?: string
  summaryMethod?: (param: { columns: TableColumn[]; data: any[] }) => string[]
  spanMethod?: (param: { row: any; column: TableColumn; rowIndex: number; columnIndex: number }) => number[] | { rowspan: number; colspan: number }
  selectOnIndeterminate?: boolean
  indent?: number
  lazy?: boolean
  load?: (row: any, treeNode: any, resolve: (data: any[]) => void) => void
  treeProps?: { hasChildren?: string; children?: string }
}

export interface TableEvents {
  'select': (selection: any[], row: any) => void
  'select-all': (selection: any[]) => void
  'selection-change': (selection: any[]) => void
  'cell-mouse-enter': (row: any, column: TableColumn, cell: HTMLElement, event: Event) => void
  'cell-mouse-leave': (row: any, column: TableColumn, cell: HTMLElement, event: Event) => void
  'cell-click': (row: any, column: TableColumn, cell: HTMLElement, event: Event) => void
  'cell-dblclick': (row: any, column: TableColumn, cell: HTMLElement, event: Event) => void
  'row-click': (row: any, column: TableColumn, event: Event) => void
  'row-contextmenu': (row: any, column: TableColumn, event: Event) => void
  'row-dblclick': (row: any, column: TableColumn, event: Event) => void
  'header-click': (column: TableColumn, event: Event) => void
  'header-contextmenu': (column: TableColumn, event: Event) => void
  'sort-change': (sort: { column: TableColumn; prop: string; order: string }) => void
  'filter-change': (filters: Record<string, any>) => void
  'current-change': (currentRow: any, oldCurrentRow: any) => void
  'header-dragend': (newWidth: number, oldWidth: number, column: TableColumn, event: Event) => void
  'expand-change': (row: any, expandedRows: any[]) => void
}

export interface TableMethods {
  clearSelection: () => void
  toggleRowSelection: (row: any, selected?: boolean) => void
  toggleAllSelection: () => void
  toggleRowExpansion: (row: any, expanded?: boolean) => void
  setCurrentRow: (row: any) => void
  clearSort: () => void
  clearFilter: (columnKey?: string) => void
  doLayout: () => void
  sort: (prop: string, order: string) => void
}

// Mobile table types
export interface MobileTableCard {
  title: string
  subtitle?: string
  fields: MobileTableField[]
  actions?: MobileTableAction[]
  expandable?: boolean
  expanded?: boolean
}

export interface MobileTableField {
  label: string
  value: any
  formatter?: (value: any) => string
  type?: 'text' | 'number' | 'date' | 'status' | 'currency'
  color?: string
  icon?: string
}

export interface MobileTableAction {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  handler: () => void
  disabled?: boolean
}

// Table state management
export interface TableState {
  data: any[]
  loading: boolean
  selection: any[]
  currentRow: any | null
  sort: TableSort
  filters: Record<string, any>
  pagination: TablePagination
  expandedRows: string[]
}

// Table utilities
export interface TableUtils {
  formatCurrency: (value: number) => string
  formatDate: (value: string | Date) => string
  formatStatus: (value: string) => { text: string; type: string }
  formatNumber: (value: number, decimals?: number) => string
  formatPercent: (value: number) => string
  formatFileSize: (value: number) => string
  truncateText: (text: string, length: number) => string
}

// Export types
export interface TableExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  filename?: string
  columns?: string[]
  includeHeader?: boolean
  includeSelection?: boolean
  customData?: any[]
}

// Filter types
export interface TableFilter {
  column: string
  value: any
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'between'
}

export interface TableFilterConfig {
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'checkbox'
  options?: { label: string; value: any }[]
  placeholder?: string
  multiple?: boolean
  clearable?: boolean
}

// Advanced table features
export interface TableGrouping {
  column: string
  order?: 'asc' | 'desc'
  showSummary?: boolean
}

export interface TableSummary {
  columns: string[]
  method: 'sum' | 'avg' | 'count' | 'max' | 'min' | 'custom'
  customMethod?: (values: any[]) => any
  formatter?: (value: any) => string
}

export interface TableVirtualScroll {
  enabled: boolean
  itemHeight: number
  bufferSize?: number
  threshold?: number
}

// Responsive table configuration
export interface ResponsiveTableConfig {
  breakpoints: {
    mobile: number
    tablet: number
    desktop: number
  }
  mobileLayout: 'card' | 'stack' | 'scroll'
  cardTemplate?: string
  stackFields?: string[]
  hideColumns?: {
    mobile?: string[]
    tablet?: string[]
  }
  adaptiveActions?: boolean
}
