<template>
  <div
    class="action-card"
    :class="[
      `action-card--${color}`,
      { 'action-card--disabled': disabled }
    ]"
    @click="handleClick"
  >
    <div class="action-card__content">
      <div class="action-card__icon">
        <el-icon>
          <component :is="iconComponent" />
        </el-icon>
      </div>
      
      <div class="action-card__body">
        <h3 class="action-card__title">{{ title }}</h3>
        <p class="action-card__description">{{ description }}</p>
      </div>
      
      <div class="action-card__arrow">
        <el-icon>
          <ArrowRight />
        </el-icon>
      </div>
    </div>
    
    <div class="action-card__overlay"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'

interface Props {
  title: string
  description: string
  icon: string
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'purple' | 'secondary'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  color: 'primary',
  disabled: false
})

const emit = defineEmits<{
  click: []
}>()

// Dynamic icon component
const iconComponent = computed(() => {
  const iconMap: Record<string, any> = {
    Search: () => import('@element-plus/icons-vue').then(m => m.Search),
    Warning: () => import('@element-plus/icons-vue').then(m => m.Warning),
    User: () => import('@element-plus/icons-vue').then(m => m.User),
    PieChart: () => import('@element-plus/icons-vue').then(m => m.PieChart),
    Document: () => import('@element-plus/icons-vue').then(m => m.Document),
    Tools: () => import('@element-plus/icons-vue').then(m => m.Tools)
  }
  
  return iconMap[props.icon] || (() => import('@element-plus/icons-vue').then(m => m.QuestionFilled))
})

function handleClick() {
  if (!props.disabled) {
    emit('click')
  }
}
</script>

<style lang="scss" scoped>
.action-card {
  position: relative;
  background: var(--color-bg-primary);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-all);
  cursor: pointer;
  overflow: hidden;
  border: 1px solid var(--color-border-light);
  
  &:hover:not(&--disabled) {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    
    .action-card__overlay {
      opacity: 1;
    }
    
    .action-card__arrow {
      transform: translateX(4px);
    }
  }
  
  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;
    
    .action-card__content {
      pointer-events: none;
    }
  }
  
  // Color variants
  &--primary {
    .action-card__icon {
      background: var(--color-primary-lighter);
      color: var(--color-primary);
    }
    
    .action-card__overlay {
      background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
    }
  }
  
  &--success {
    .action-card__icon {
      background: rgba(103, 194, 58, 0.1);
      color: var(--color-success);
    }
    
    .action-card__overlay {
      background: linear-gradient(135deg, var(--color-success), var(--color-success-dark));
    }
  }
  
  &--warning {
    .action-card__icon {
      background: rgba(230, 162, 60, 0.1);
      color: var(--color-warning);
    }
    
    .action-card__overlay {
      background: linear-gradient(135deg, var(--color-warning), var(--color-warning-dark));
    }
  }
  
  &--danger {
    .action-card__icon {
      background: rgba(245, 108, 108, 0.1);
      color: var(--color-danger);
    }
    
    .action-card__overlay {
      background: linear-gradient(135deg, var(--color-danger), var(--color-danger-dark));
    }
  }
  
  &--info {
    .action-card__icon {
      background: rgba(144, 147, 153, 0.1);
      color: var(--color-info);
    }
    
    .action-card__overlay {
      background: linear-gradient(135deg, var(--color-info), var(--color-info-dark));
    }
  }
  
  &--purple {
    .action-card__icon {
      background: rgba(139, 69, 219, 0.1);
      color: #8b45db;
    }
    
    .action-card__overlay {
      background: linear-gradient(135deg, #8b45db, #7c3aed);
    }
  }
  
  &--secondary {
    .action-card__icon {
      background: var(--color-gray-100);
      color: var(--color-gray-600);
    }
    
    .action-card__overlay {
      background: linear-gradient(135deg, var(--color-gray-500), var(--color-gray-600));
    }
  }
  
  &__content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }
  
  &__icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
  }
  
  &__body {
    flex: 1;
    min-width: 0;
  }
  
  &__title {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text-primary);
    line-height: 1.3;
  }
  
  &__description {
    margin: 0;
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    line-height: 1.4;
  }
  
  &__arrow {
    color: var(--color-text-secondary);
    font-size: 1.25rem;
    transition: var(--transition-transform);
    flex-shrink: 0;
    margin-top: 0.25rem;
  }
  
  &__overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0;
    transition: var(--transition-opacity);
    z-index: 1;
  }
}

// Responsive design
@media (max-width: 768px) {
  .action-card {
    padding: 1.25rem;
    
    &__content {
      gap: 0.75rem;
    }
    
    &__icon {
      width: 2.5rem;
      height: 2.5rem;
      font-size: 1.25rem;
    }
    
    &__title {
      font-size: 1rem;
    }
    
    &__description {
      font-size: 0.8rem;
    }
  }
}

// Dark mode support
.dark {
  .action-card {
    background: var(--color-bg-primary);
    border-color: var(--color-border);
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .action-card {
    border: 2px solid var(--color-border-dark);
    
    &__icon {
      border: 1px solid currentColor;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .action-card {
    transition: none;
    
    &:hover {
      transform: none;
    }
    
    &__arrow {
      transition: none;
    }
    
    &__overlay {
      transition: none;
    }
  }
}

// Focus styles
.action-card:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// Touch device optimizations
@media (hover: none) and (pointer: coarse) {
  .action-card {
    &:active:not(&--disabled) {
      transform: scale(0.98);
    }
  }
}
</style>
