import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// WindiCSS
import 'virtual:windi.css'

// Global styles
import './styles/main.scss'

// Touch events for mobile
import Vue3TouchEvents from 'vue3-touch-events'

// Progress bar
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// Viewport units buggyfill for mobile
import 'viewport-units-buggyfill/viewport-units-buggyfill'

// Configure NProgress
NProgress.configure({
  showSpinner: false,
  trickleSpeed: 200
})

const app = createApp(App)

// Install plugins
app.use(createPinia())
app.use(router)
app.use(ElementPlus)
app.use(Vue3TouchEvents)

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// Global properties
app.config.globalProperties.$ELEMENT = {
  size: 'default',
  zIndex: 3000
}

// Error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
  // You can send error to monitoring service here
}

// Mount app
app.mount('#app')

// Router guards for progress bar
router.beforeEach((to, from, next) => {
  NProgress.start()
  next()
})

router.afterEach(() => {
  NProgress.done()
})

// PWA update prompt
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration)
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}
