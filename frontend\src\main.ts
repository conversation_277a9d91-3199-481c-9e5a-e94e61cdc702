import { createApp } from 'vue'
import App from './App.vue'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// WindiCSS
import 'virtual:windi.css'

const app = createApp(App)

// Install plugins
app.use(ElementPlus)

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// Error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
}

// Mount app
app.mount('#app')
