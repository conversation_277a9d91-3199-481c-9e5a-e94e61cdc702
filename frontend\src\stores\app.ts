import { defineStore } from 'pinia'
import type { RouteLocationNormalized } from 'vue-router'

export interface AppState {
  // Loading state
  isLoading: boolean
  loadingText: string
  
  // UI state
  isMobile: boolean
  isTablet: boolean
  sidebarCollapsed: boolean
  mobileMenuOpen: boolean
  
  // Viewport
  viewport: {
    width: number
    height: number
    orientation: 'portrait' | 'landscape'
  }
  
  // Theme
  theme: 'light' | 'dark'
  
  // Current route
  currentRoute: RouteLocationNormalized | null
  
  // App settings
  settings: {
    pageSize: number
    autoRefresh: boolean
    refreshInterval: number
    showNotifications: boolean
    compactMode: boolean
  }
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    isLoading: false,
    loadingText: '加载中...',
    
    isMobile: false,
    isTablet: false,
    sidebarCollapsed: false,
    mobileMenuOpen: false,
    
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight,
      orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
    },
    
    theme: 'light',
    currentRoute: null,
    
    settings: {
      pageSize: 20,
      autoRefresh: false,
      refreshInterval: 30000,
      showNotifications: true,
      compactMode: false
    }
  }),
  
  getters: {
    // Device detection
    isDesktop: (state) => !state.isMobile && !state.isTablet,
    
    // Responsive breakpoints
    isSmallScreen: (state) => state.viewport.width < 640,
    isMediumScreen: (state) => state.viewport.width >= 640 && state.viewport.width < 1024,
    isLargeScreen: (state) => state.viewport.width >= 1024,
    
    // Layout helpers
    shouldShowSidebar: (state) => {
      if (state.isMobile) return state.mobileMenuOpen
      return !state.sidebarCollapsed
    },
    
    mainContentClass: (state) => {
      const classes = ['main-content', 'transition-all', 'duration-300']
      
      if (state.isMobile) {
        classes.push('ml-0')
      } else if (state.sidebarCollapsed) {
        classes.push('ml-16')
      } else {
        classes.push('ml-64')
      }
      
      return classes.join(' ')
    }
  },
  
  actions: {
    // Loading state
    setLoading(loading: boolean, text = '加载中...') {
      this.isLoading = loading
      this.loadingText = text
    },
    
    // Initialize app
    async initialize() {
      this.updateViewport()
      this.loadSettings()
      this.detectDevice()
      
      // Setup auto-refresh if enabled
      if (this.settings.autoRefresh) {
        this.setupAutoRefresh()
      }
    },
    
    // Viewport management
    updateViewport() {
      this.viewport.width = window.innerWidth
      this.viewport.height = window.innerHeight
      this.viewport.orientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
      
      this.detectDevice()
    },
    
    // Device detection
    detectDevice() {
      const width = this.viewport.width
      this.isMobile = width < 768
      this.isTablet = width >= 768 && width < 1024
      
      // Auto-collapse sidebar on mobile
      if (this.isMobile && !this.sidebarCollapsed) {
        this.sidebarCollapsed = true
      }
    },
    
    // Sidebar management
    toggleSidebar() {
      if (this.isMobile) {
        this.mobileMenuOpen = !this.mobileMenuOpen
      } else {
        this.sidebarCollapsed = !this.sidebarCollapsed
      }
    },
    
    closeMobileMenu() {
      if (this.isMobile) {
        this.mobileMenuOpen = false
      }
    },
    
    // Theme management
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
      this.saveSettings()
      this.applyTheme()
    },
    
    applyTheme() {
      document.documentElement.classList.toggle('dark', this.theme === 'dark')
    },
    
    // Route management
    setCurrentRoute(route: RouteLocationNormalized) {
      this.currentRoute = route
    },
    
    // Settings management
    updateSettings(newSettings: Partial<AppState['settings']>) {
      this.settings = { ...this.settings, ...newSettings }
      this.saveSettings()
      
      // Apply auto-refresh setting
      if ('autoRefresh' in newSettings) {
        if (newSettings.autoRefresh) {
          this.setupAutoRefresh()
        } else {
          this.clearAutoRefresh()
        }
      }
    },
    
    loadSettings() {
      try {
        const saved = localStorage.getItem('app-settings')
        if (saved) {
          const settings = JSON.parse(saved)
          this.settings = { ...this.settings, ...settings }
        }
        
        // Load theme
        const savedTheme = localStorage.getItem('app-theme') as 'light' | 'dark'
        if (savedTheme) {
          this.theme = savedTheme
          this.applyTheme()
        }
        
        // Load sidebar state
        const sidebarState = localStorage.getItem('sidebar-collapsed')
        if (sidebarState !== null) {
          this.sidebarCollapsed = JSON.parse(sidebarState)
        }
      } catch (error) {
        console.error('Failed to load settings:', error)
      }
    },
    
    saveSettings() {
      try {
        localStorage.setItem('app-settings', JSON.stringify(this.settings))
        localStorage.setItem('app-theme', this.theme)
        localStorage.setItem('sidebar-collapsed', JSON.stringify(this.sidebarCollapsed))
      } catch (error) {
        console.error('Failed to save settings:', error)
      }
    },
    
    // Auto-refresh management
    private refreshTimer: number | null = null,
    
    setupAutoRefresh() {
      this.clearAutoRefresh()
      
      if (this.settings.autoRefresh && this.settings.refreshInterval > 0) {
        this.refreshTimer = window.setInterval(() => {
          // Emit refresh event
          window.dispatchEvent(new CustomEvent('app:auto-refresh'))
        }, this.settings.refreshInterval)
      }
    },
    
    clearAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    }
  }
})
