<template>
  <div class="responsive-table-container">
    <!-- Desktop Table View -->
    <div v-if="!isMobileView" class="desktop-table">
      <el-table
        ref="tableRef"
        :data="data"
        :loading="loading"
        :height="height"
        :max-height="maxHeight"
        stripe
        border
        class="w-full"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- Selection Column -->
        <el-table-column
          v-if="selectable"
          type="selection"
          width="55"
          fixed="left"
        />
        
        <!-- Index Column -->
        <el-table-column
          v-if="showIndex"
          type="index"
          label="#"
          width="60"
          fixed="left"
        />
        
        <!-- Dynamic Columns -->
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :sortable="column.sortable"
          :fixed="column.fixed"
          :align="column.align || 'left'"
          :show-overflow-tooltip="column.showTooltip !== false"
        >
          <template #default="scope">
            <slot
              :name="column.prop"
              :row="scope.row"
              :column="column"
              :index="scope.$index"
            >
              <span v-if="column.formatter">
                {{ column.formatter(scope.row[column.prop], scope.row) }}
              </span>
              <span v-else>{{ scope.row[column.prop] }}</span>
            </slot>
          </template>
        </el-table-column>
        
        <!-- Actions Column -->
        <el-table-column
          v-if="$slots.actions"
          label="操作"
          :width="actionsWidth"
          fixed="right"
          align="center"
        >
          <template #default="scope">
            <slot
              name="actions"
              :row="scope.row"
              :index="scope.$index"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- Mobile Card View -->
    <div v-else class="mobile-cards">
      <div
        v-for="(item, index) in data"
        :key="getRowKey(item, index)"
        class="mobile-card"
        @click="handleCardClick(item, index)"
      >
        <div class="card-header">
          <div class="card-title">
            <slot name="card-title" :row="item" :index="index">
              {{ item[primaryColumn] || `项目 ${index + 1}` }}
            </slot>
          </div>
          <div v-if="$slots.actions" class="card-actions">
            <slot name="actions" :row="item" :index="index" />
          </div>
        </div>
        
        <div class="card-content">
          <div
            v-for="column in mobileColumns"
            :key="column.prop"
            class="card-field"
          >
            <span class="field-label">{{ column.label }}:</span>
            <span class="field-value">
              <slot
                :name="column.prop"
                :row="item"
                :column="column"
                :index="index"
              >
                <span v-if="column.formatter">
                  {{ column.formatter(item[column.prop], item) }}
                </span>
                <span v-else>{{ item[column.prop] }}</span>
              </slot>
            </span>
          </div>
        </div>
        
        <!-- Expandable content -->
        <div
          v-if="expandable && expandedRows.includes(getRowKey(item, index))"
          class="card-expanded"
        >
          <slot name="expanded" :row="item" :index="index" />
        </div>
      </div>
      
      <!-- Mobile Loading -->
      <div v-if="loading" class="mobile-loading">
        <el-skeleton :rows="3" animated />
      </div>
      
      <!-- Mobile Empty -->
      <div v-if="!loading && data.length === 0" class="mobile-empty">
        <el-empty description="暂无数据" />
      </div>
    </div>
    
    <!-- Pagination -->
    <div
      v-if="pagination && total > 0"
      class="table-pagination"
      :class="{ 'mobile-pagination': isMobileView }"
    >
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="pageSizes"
        :layout="paginationLayout"
        :small="isMobileView"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useAppStore } from '@/stores/app'
import type { TableColumn } from '@/types/table'

interface Props {
  data: any[]
  columns: TableColumn[]
  loading?: boolean
  height?: string | number
  maxHeight?: string | number
  selectable?: boolean
  showIndex?: boolean
  expandable?: boolean
  pagination?: boolean
  total?: number
  pageSize?: number
  pageSizes?: number[]
  actionsWidth?: number
  primaryColumn?: string
  rowKey?: string | ((row: any) => string)
  mobileBreakpoint?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selectable: false,
  showIndex: false,
  expandable: false,
  pagination: true,
  total: 0,
  pageSize: 20,
  pageSizes: () => [10, 20, 50, 100],
  actionsWidth: 150,
  primaryColumn: 'name',
  rowKey: 'id',
  mobileBreakpoint: 768
})

const emit = defineEmits<{
  'selection-change': [selection: any[]]
  'sort-change': [sort: { prop: string; order: string }]
  'size-change': [size: number]
  'current-change': [page: number]
  'row-click': [row: any, index: number]
}>()

const appStore = useAppStore()
const tableRef = ref()
const expandedRows = ref<string[]>([])
const currentPage = ref(1)

// Responsive detection
const isMobileView = computed(() => {
  return appStore.viewport.width < props.mobileBreakpoint
})

// Visible columns for desktop
const visibleColumns = computed(() => {
  return props.columns.filter(col => !col.hidden)
})

// Mobile columns (exclude actions and less important columns)
const mobileColumns = computed(() => {
  return props.columns.filter(col => 
    !col.hidden && 
    col.prop !== 'actions' && 
    col.mobileVisible !== false
  ).slice(0, 4) // Limit to 4 fields on mobile
})

// Pagination layout
const paginationLayout = computed(() => {
  if (isMobileView.value) {
    return 'prev, pager, next'
  }
  return 'total, sizes, prev, pager, next, jumper'
})

// Get row key
function getRowKey(row: any, index: number): string {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row)
  }
  return row[props.rowKey] || index.toString()
}

// Event handlers
function handleSelectionChange(selection: any[]) {
  emit('selection-change', selection)
}

function handleSortChange(sort: { prop: string; order: string }) {
  emit('sort-change', sort)
}

function handleSizeChange(size: number) {
  emit('size-change', size)
}

function handleCurrentChange(page: number) {
  emit('current-change', page)
}

function handleCardClick(row: any, index: number) {
  if (props.expandable) {
    const key = getRowKey(row, index)
    const expandedIndex = expandedRows.value.indexOf(key)
    if (expandedIndex > -1) {
      expandedRows.value.splice(expandedIndex, 1)
    } else {
      expandedRows.value.push(key)
    }
  }
  emit('row-click', row, index)
}

// Watch for mobile view changes
watch(isMobileView, (newVal) => {
  if (!newVal && tableRef.value) {
    // Refresh table layout when switching back to desktop
    nextTick(() => {
      tableRef.value.doLayout()
    })
  }
})
</script>

<style lang="scss" scoped>
.responsive-table-container {
  .desktop-table {
    :deep(.el-table) {
      .el-table__header-wrapper {
        th {
          background-color: #f8f9fa;
          color: #495057;
          font-weight: 600;
        }
      }
      
      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
  
  .mobile-cards {
    .mobile-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 12px;
      padding: 16px;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .card-title {
          font-weight: 600;
          color: #303133;
          font-size: 16px;
        }
        
        .card-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .card-content {
        .card-field {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .field-label {
            color: #909399;
            font-size: 14px;
            min-width: 80px;
          }
          
          .field-value {
            color: #303133;
            font-size: 14px;
            text-align: right;
            flex: 1;
          }
        }
      }
      
      .card-expanded {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #ebeef5;
      }
    }
    
    .mobile-loading,
    .mobile-empty {
      padding: 20px;
      text-align: center;
    }
  }
  
  .table-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    
    &.mobile-pagination {
      :deep(.el-pagination) {
        .el-pagination__total,
        .el-pagination__sizes,
        .el-pagination__jump {
          display: none;
        }
      }
    }
  }
}
</style>
