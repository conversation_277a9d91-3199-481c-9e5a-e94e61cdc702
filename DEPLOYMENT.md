# 太享查询系统 - 部署指南

本文档详细说明如何部署重构后的前端系统，包括开发环境和生产环境的配置。

## 📋 系统要求

### 开发环境
- **Node.js**: 16.x 或更高版本
- **npm**: 8.x 或更高版本
- **Python**: 3.8 或更高版本
- **Flask**: 2.x 或更高版本

### 生产环境
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+
- **应用服务器**: Gunicorn 或 uWSGI
- **数据库**: MySQL 8.0+ 或 PostgreSQL 12+
- **缓存**: Redis 6.0+ (可选)

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd hdsc_query_app_02
```

### 2. 安装前端依赖
```bash
cd frontend
npm install
```

### 3. 安装后端依赖
```bash
cd ..
pip install -r requirements.txt
```

### 4. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

### 5. 构建前端
```bash
cd frontend
npm run build
```

### 6. 启动应用
```bash
cd ..
python app.py
```

访问 http://localhost:5000 查看应用。

## 🔧 开发环境配置

### 前端开发服务器
```bash
cd frontend
npm run dev
```
- 前端开发服务器: http://localhost:3000
- 热重载和实时编译
- 自动代理API请求到后端

### 后端开发服务器
```bash
python app.py
```
- 后端API服务器: http://localhost:5000
- Flask调试模式
- 自动重载代码变更

### 并行开发
使用两个终端窗口同时运行前后端：

**终端1 (前端):**
```bash
cd frontend
npm run dev
```

**终端2 (后端):**
```bash
python app.py
```

## 🏗️ 生产环境部署

### 方案一：传统部署

#### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y nginx python3 python3-pip nodejs npm git
```

#### 2. 项目部署
```bash
# 克隆项目
git clone <repository-url> /var/www/hdsc_query_app
cd /var/www/hdsc_query_app

# 安装Python依赖
pip3 install -r requirements.txt

# 构建前端
cd frontend
npm install
npm run build
cd ..

# 设置权限
sudo chown -R www-data:www-data /var/www/hdsc_query_app
sudo chmod -R 755 /var/www/hdsc_query_app
```

#### 3. Nginx配置
```nginx
# /etc/nginx/sites-available/hdsc_query_app
server {
    listen 80;
    server_name your-domain.com;
    
    # 静态文件
    location /static/ {
        alias /var/www/hdsc_query_app/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 前端资源
    location /static/dist/ {
        alias /var/www/hdsc_query_app/app/static/dist/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Gzip压缩
        gzip on;
        gzip_types text/css application/javascript application/json;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 前端路由
    location / {
        try_files $uri $uri/ @fallback;
    }
    
    location @fallback {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 4. 启用站点
```bash
sudo ln -s /etc/nginx/sites-available/hdsc_query_app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 5. Gunicorn配置
```bash
# 安装Gunicorn
pip3 install gunicorn

# 创建Gunicorn配置
cat > /var/www/hdsc_query_app/gunicorn.conf.py << EOF
bind = "127.0.0.1:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
EOF
```

#### 6. 系统服务
```bash
# 创建systemd服务
sudo cat > /etc/systemd/system/hdsc_query_app.service << EOF
[Unit]
Description=HDSC Query App
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/hdsc_query_app
Environment=PATH=/var/www/hdsc_query_app/venv/bin
ExecStart=/usr/local/bin/gunicorn -c gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable hdsc_query_app
sudo systemctl start hdsc_query_app
```

### 方案二：Docker部署

#### 1. Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS frontend-build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 主应用Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 复制前端构建文件
COPY --from=frontend-build /app/dist ./app/static/dist/

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "-c", "gunicorn.conf.py", "app:app"]
```

#### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=mysql://user:password@db:3306/hdsc_query
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: hdsc_query
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mysql_data:
```

#### 3. 部署命令
```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f app

# 更新部署
docker-compose pull
docker-compose up -d --force-recreate
```

## 🔒 SSL/HTTPS配置

### Let's Encrypt (免费SSL)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Nginx HTTPS配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 📊 监控和日志

### 应用监控
```bash
# 安装监控工具
pip install flask-monitoring-dashboard

# 配置监控
# 在app.py中添加:
# import flask_monitoring_dashboard as dashboard
# dashboard.bind(app)
```

### 日志配置
```python
# config.py
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler(
        'logs/hdsc_query_app.log', 
        maxBytes=10240000, 
        backupCount=10
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
```

### Nginx日志分析
```bash
# 安装GoAccess
sudo apt install goaccess

# 实时分析
sudo goaccess /var/log/nginx/access.log -c
```

## 🔄 更新和维护

### 自动化部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 开始部署..."

# 拉取最新代码
git pull origin main

# 构建前端
cd frontend
npm install
npm run build
cd ..

# 重启服务
sudo systemctl restart hdsc_query_app
sudo systemctl reload nginx

echo "✅ 部署完成!"
```

### 数据库迁移
```bash
# 备份数据库
mysqldump -u user -p hdsc_query > backup_$(date +%Y%m%d_%H%M%S).sql

# 运行迁移
python manage.py db upgrade
```

### 性能优化
```bash
# 启用Gzip压缩
# 在Nginx配置中添加:
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/css application/javascript application/json;

# 启用浏览器缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 🐛 故障排除

### 常见问题

1. **前端资源404错误**
   ```bash
   # 检查构建文件
   ls -la app/static/dist/
   
   # 重新构建
   cd frontend && npm run build
   ```

2. **API请求失败**
   ```bash
   # 检查后端服务
   sudo systemctl status hdsc_query_app
   
   # 查看日志
   sudo journalctl -u hdsc_query_app -f
   ```

3. **数据库连接错误**
   ```bash
   # 检查数据库服务
   sudo systemctl status mysql
   
   # 测试连接
   mysql -u user -p -h localhost hdsc_query
   ```

### 日志位置
- **应用日志**: `/var/www/hdsc_query_app/logs/`
- **Nginx日志**: `/var/log/nginx/`
- **系统日志**: `sudo journalctl -u hdsc_query_app`

## 📞 技术支持

如遇到部署问题，请检查：
1. 系统要求是否满足
2. 依赖是否正确安装
3. 配置文件是否正确
4. 服务是否正常运行
5. 防火墙和端口设置

更多技术支持，请联系开发团队。
