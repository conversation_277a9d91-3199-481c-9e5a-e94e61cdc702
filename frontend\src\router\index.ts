import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress'

// Lazy load components
const Layout = () => import('@/layouts/DefaultLayout.vue')
const Login = () => import('@/views/auth/Login.vue')
const Home = () => import('@/views/Home.vue')
const DataQuery = () => import('@/views/query/DataQuery.vue')
const OverdueOrders = () => import('@/views/query/OverdueOrders.vue')
const CustomerSummary = () => import('@/views/summary/CustomerSummary.vue')
const DataSummary = () => import('@/views/summary/DataSummary.vue')
const ContractGenerator = () => import('@/views/tools/ContractGenerator.vue')
const ReceiptGenerator = () => import('@/views/tools/ReceiptGenerator.vue')
const OrderCleaner = () => import('@/views/tools/OrderCleaner.vue')
const NotFound = () => import('@/views/error/NotFound.vue')

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false,
      transition: 'slide-up'
    }
  },
  {
    path: '/',
    component: Layout,
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'Home',
        component: Home,
        meta: {
          title: '首页',
          icon: 'House',
          transition: 'fade'
        }
      },
      {
        path: '/query',
        name: 'DataQuery',
        component: DataQuery,
        meta: {
          title: '数据查询',
          icon: 'Search',
          transition: 'slide-up'
        }
      },
      {
        path: '/overdue',
        name: 'OverdueOrders',
        component: OverdueOrders,
        meta: {
          title: '逾期订单',
          icon: 'Warning',
          transition: 'slide-up'
        }
      },
      {
        path: '/customer-summary',
        name: 'CustomerSummary',
        component: CustomerSummary,
        meta: {
          title: '客户汇总',
          icon: 'User',
          transition: 'slide-up'
        }
      },
      {
        path: '/summary',
        name: 'DataSummary',
        component: DataSummary,
        meta: {
          title: '数据汇总',
          icon: 'PieChart',
          requiresPermission: 'full',
          transition: 'slide-up'
        }
      },
      {
        path: '/tools',
        meta: {
          title: '工具',
          icon: 'Tools'
        },
        children: [
          {
            path: 'contract',
            name: 'ContractGenerator',
            component: ContractGenerator,
            meta: {
              title: '合同生成',
              icon: 'Document',
              transition: 'slide-up'
            }
          },
          {
            path: 'receipt',
            name: 'ReceiptGenerator',
            component: ReceiptGenerator,
            meta: {
              title: '回执单生成',
              icon: 'Receipt',
              transition: 'slide-up'
            }
          },
          {
            path: 'order-cleaner',
            name: 'OrderCleaner',
            component: OrderCleaner,
            meta: {
              title: '订单清理',
              icon: 'Delete',
              requiresPermission: 'full',
              transition: 'slide-up'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      transition: 'fade'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const appStore = useAppStore()
  
  // Set page title
  if (to.meta.title) {
    document.title = `${to.meta.title} - 太享查询系统`
  }
  
  // Check authentication
  if (to.meta.requiresAuth !== false) {
    if (!userStore.isAuthenticated) {
      // Try to restore session
      try {
        await userStore.restoreSession()
      } catch (error) {
        console.error('Session restore failed:', error)
      }
      
      if (!userStore.isAuthenticated) {
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
    
    // Check permissions
    if (to.meta.requiresPermission) {
      if (!userStore.hasPermission(to.meta.requiresPermission as string)) {
        ElMessage.error('您没有访问此页面的权限')
        next({ name: 'Home' })
        return
      }
    }
  }
  
  // Redirect to home if already authenticated and trying to access login
  if (to.name === 'Login' && userStore.isAuthenticated) {
    next({ name: 'Home' })
    return
  }
  
  // Update app state
  appStore.setCurrentRoute(to)
  
  next()
})

router.afterEach((to, from) => {
  NProgress.done()
  
  // Update mobile navigation state
  const appStore = useAppStore()
  if (appStore.isMobile) {
    appStore.closeMobileMenu()
  }
})

router.onError((error) => {
  console.error('Router error:', error)
  NProgress.done()
  ElMessage.error('页面加载失败，请重试')
})

export default router
