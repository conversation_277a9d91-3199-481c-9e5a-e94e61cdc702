export type UserPermission = 'limited' | 'standard' | 'full'

export interface User {
  id: string
  username: string
  name: string
  email?: string
  role: string
  avatar?: string
  createdAt: string
  lastLoginAt?: string
}

export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
}

export interface LoginResponse {
  user: User
  token: string
  permissions: UserPermission[]
  expiresIn: number
}

export interface UserProfile {
  name: string
  email?: string
  avatar?: string
  preferences: {
    theme: 'light' | 'dark'
    language: string
    timezone: string
    notifications: boolean
  }
}
