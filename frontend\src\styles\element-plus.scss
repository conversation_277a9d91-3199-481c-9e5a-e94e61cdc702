// Element Plus theme customization

// Override Element Plus CSS variables
:root {
  // Primary color
  --el-color-primary: var(--color-primary);
  --el-color-primary-light-3: var(--color-primary-light);
  --el-color-primary-light-5: var(--color-primary-lighter);
  --el-color-primary-light-7: var(--color-primary-lighter);
  --el-color-primary-light-8: var(--color-primary-lighter);
  --el-color-primary-light-9: var(--color-primary-lighter);
  --el-color-primary-dark-2: var(--color-primary-dark);
  
  // Success color
  --el-color-success: var(--color-success);
  --el-color-success-light-3: var(--color-success-light);
  --el-color-success-dark-2: var(--color-success-dark);
  
  // Warning color
  --el-color-warning: var(--color-warning);
  --el-color-warning-light-3: var(--color-warning-light);
  --el-color-warning-dark-2: var(--color-warning-dark);
  
  // Danger color
  --el-color-danger: var(--color-danger);
  --el-color-danger-light-3: var(--color-danger-light);
  --el-color-danger-dark-2: var(--color-danger-dark);
  
  // Info color
  --el-color-info: var(--color-info);
  --el-color-info-light-3: var(--color-info-light);
  --el-color-info-dark-2: var(--color-info-dark);
  
  // Text colors
  --el-text-color-primary: var(--color-text-primary);
  --el-text-color-regular: var(--color-text-regular);
  --el-text-color-secondary: var(--color-text-secondary);
  --el-text-color-placeholder: var(--color-text-placeholder);
  --el-text-color-disabled: var(--color-text-disabled);
  
  // Background colors
  --el-bg-color: var(--color-bg-primary);
  --el-bg-color-page: var(--color-bg-page);
  --el-bg-color-overlay: var(--color-bg-secondary);
  
  // Border colors
  --el-border-color: var(--color-border);
  --el-border-color-light: var(--color-border-light);
  --el-border-color-lighter: var(--color-border-lighter);
  --el-border-color-extra-light: var(--color-border-extra-light);
  --el-border-color-dark: var(--color-border-dark);
  --el-border-color-darker: var(--color-border-darker);
  
  // Typography
  --el-font-family: var(--font-family-base);
  --el-font-size-base: var(--font-size-base);
  --el-font-size-small: var(--font-size-sm);
  --el-font-size-large: var(--font-size-lg);
  --el-font-size-extra-large: var(--font-size-xl);
  
  // Border radius
  --el-border-radius-base: var(--radius-base);
  --el-border-radius-small: var(--radius-sm);
  --el-border-radius-round: var(--radius-full);
  
  // Box shadow
  --el-box-shadow: var(--shadow-base);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-dark: var(--shadow-lg);
  
  // Component specific
  --el-component-size: 32px;
  --el-component-size-large: 40px;
  --el-component-size-small: 24px;
}

// Component customizations
.el-button {
  font-weight: 500;
  border-radius: var(--radius-lg);
  transition: var(--transition-all);
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  // Size variants
  &.el-button--large {
    padding: 12px 20px;
    font-size: var(--font-size-lg);
  }
  
  &.el-button--small {
    padding: 6px 12px;
    font-size: var(--font-size-sm);
  }
}

.el-card {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-all);
  
  &:hover {
    box-shadow: var(--shadow-md);
  }
  
  .el-card__header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--color-border-lighter);
  }
  
  .el-card__body {
    padding: 1.5rem;
  }
}

.el-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
  
  .el-table__header-wrapper {
    th {
      background-color: var(--color-bg-secondary);
      color: var(--color-text-primary);
      font-weight: 600;
      border-bottom: 2px solid var(--color-border);
    }
  }
  
  .el-table__row {
    transition: var(--transition-colors);
    
    &:hover {
      background-color: var(--color-bg-secondary);
    }
  }
}

.el-input {
  .el-input__wrapper {
    border-radius: var(--radius-lg);
    transition: var(--transition-all);
    
    &:hover {
      box-shadow: var(--shadow-sm);
    }
    
    &.is-focus {
      box-shadow: 0 0 0 2px var(--color-primary-lighter);
    }
  }
}

.el-select {
  .el-select__wrapper {
    border-radius: var(--radius-lg);
  }
}

.el-dialog {
  border-radius: var(--radius-2xl);
  overflow: hidden;
  
  .el-dialog__header {
    padding: 1.5rem;
    background-color: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border-lighter);
  }
  
  .el-dialog__body {
    padding: 1.5rem;
  }
  
  .el-dialog__footer {
    padding: 1rem 1.5rem 1.5rem;
    border-top: 1px solid var(--color-border-lighter);
  }
}

.el-drawer {
  .el-drawer__header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--color-border-lighter);
  }
  
  .el-drawer__body {
    padding: 1.5rem;
  }
}

.el-menu {
  border-radius: var(--radius-lg);
  
  .el-menu-item {
    border-radius: var(--radius-md);
    margin: 0.25rem 0.5rem;
    transition: var(--transition-all);
    
    &:hover {
      background-color: var(--color-primary-lighter);
      color: var(--color-primary);
    }
    
    &.is-active {
      background-color: var(--color-primary);
      color: white;
    }
  }
}

.el-pagination {
  .el-pager li {
    border-radius: var(--radius-md);
    margin: 0 0.125rem;
    transition: var(--transition-all);
    
    &:hover {
      background-color: var(--color-primary-lighter);
      color: var(--color-primary);
    }
    
    &.is-active {
      background-color: var(--color-primary);
      color: white;
    }
  }
  
  .btn-prev,
  .btn-next {
    border-radius: var(--radius-md);
    transition: var(--transition-all);
    
    &:hover {
      background-color: var(--color-primary-lighter);
      color: var(--color-primary);
    }
  }
}

.el-message {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.el-notification {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(10px);
}

.el-loading-mask {
  backdrop-filter: blur(4px);
}

// Mobile optimizations
@media (max-width: 768px) {
  .el-button {
    min-height: 44px;
    padding: 12px 16px;
  }
  
  .el-input {
    .el-input__wrapper {
      min-height: 44px;
    }
  }
  
  .el-select {
    .el-select__wrapper {
      min-height: 44px;
    }
  }
  
  .el-dialog {
    margin: 1rem;
    width: calc(100% - 2rem);
    max-width: none;
  }
  
  .el-drawer {
    width: 85% !important;
  }
  
  .el-table {
    font-size: var(--font-size-sm);
  }
  
  .el-pagination {
    .el-pagination__sizes,
    .el-pagination__jump {
      display: none;
    }
  }
}

// Dark mode support
.dark {
  .el-card {
    background-color: var(--color-bg-primary);
    border-color: var(--color-border);
  }
  
  .el-table {
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
  }
  
  .el-dialog {
    background-color: var(--color-bg-primary);
  }
  
  .el-drawer {
    background-color: var(--color-bg-primary);
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .el-button {
    border-width: 2px;
  }
  
  .el-input {
    .el-input__wrapper {
      border-width: 2px;
    }
  }
  
  .el-table {
    .el-table__header-wrapper th {
      border-bottom-width: 3px;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .el-button,
  .el-card,
  .el-table__row,
  .el-input__wrapper,
  .el-menu-item,
  .el-pager li {
    transition: none;
  }
}
