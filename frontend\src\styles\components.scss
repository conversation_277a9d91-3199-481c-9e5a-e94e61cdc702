// Component-specific styles

// Page layout
.page-container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 1rem;
  
  @media (min-width: 1280px) {
    padding: 0 2rem;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border-lighter);
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .page-title {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-text-primary);
    line-height: 1.2;
    
    @media (max-width: 768px) {
      font-size: 1.75rem;
    }
  }
  
  .page-subtitle {
    margin: 0.5rem 0 0 0;
    color: var(--color-text-secondary);
    font-size: 1.125rem;
    
    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }
  
  .page-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    
    @media (max-width: 768px) {
      justify-content: stretch;
      
      .el-button {
        flex: 1;
      }
    }
  }
}

// Section headers
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  .section-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .section-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }
}

// Grid layouts
.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  
  @media (max-width: 1280px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.grid-auto {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Card components
.card {
  background: var(--color-bg-primary);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  transition: var(--transition-all);
  
  &:hover {
    box-shadow: var(--shadow-md);
  }
  
  &--interactive {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }
  
  &--compact {
    padding: 1rem;
  }
  
  &--large {
    padding: 2rem;
  }
  
  @media (max-width: 768px) {
    padding: 1.25rem;
    border-radius: var(--radius-lg);
    
    &--compact {
      padding: 1rem;
    }
    
    &--large {
      padding: 1.5rem;
    }
  }
}

// Loading states
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  
  .loading-content {
    text-align: center;
    
    .loading-icon {
      font-size: 2rem;
      color: var(--color-primary);
      margin-bottom: 1rem;
    }
    
    .loading-text {
      color: var(--color-text-secondary);
      font-size: 0.875rem;
    }
  }
}

// Empty states
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  
  .empty-icon {
    font-size: 4rem;
    color: var(--color-text-placeholder);
    margin-bottom: 1rem;
  }
  
  .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-text-secondary);
    margin-bottom: 0.5rem;
  }
  
  .empty-description {
    color: var(--color-text-placeholder);
    margin-bottom: 1.5rem;
  }
  
  .empty-actions {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }
  }
}

// Status indicators
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  
  &--success {
    background: rgba(103, 194, 58, 0.1);
    color: var(--color-success);
  }
  
  &--warning {
    background: rgba(230, 162, 60, 0.1);
    color: var(--color-warning);
  }
  
  &--danger {
    background: rgba(245, 108, 108, 0.1);
    color: var(--color-danger);
  }
  
  &--info {
    background: rgba(144, 147, 153, 0.1);
    color: var(--color-info);
  }
  
  &--primary {
    background: var(--color-primary-lighter);
    color: var(--color-primary);
  }
  
  .status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: currentColor;
  }
}

// Badges
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.5rem;
  height: 1.5rem;
  padding: 0 0.5rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  
  &--primary {
    background: var(--color-primary);
    color: white;
  }
  
  &--success {
    background: var(--color-success);
    color: white;
  }
  
  &--warning {
    background: var(--color-warning);
    color: white;
  }
  
  &--danger {
    background: var(--color-danger);
    color: white;
  }
  
  &--info {
    background: var(--color-info);
    color: white;
  }
  
  &--outline {
    background: transparent;
    border: 1px solid currentColor;
  }
}

// Avatars
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  background: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  font-weight: 600;
  
  &--small {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }
  
  &--medium {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
  
  &--large {
    width: 3rem;
    height: 3rem;
    font-size: 1.125rem;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// Dividers
.divider {
  border: none;
  height: 1px;
  background: var(--color-border-lighter);
  margin: 1.5rem 0;
  
  &--vertical {
    width: 1px;
    height: auto;
    margin: 0 1rem;
  }
  
  &--thick {
    height: 2px;
    background: var(--color-border);
  }
}

// Responsive utilities
.mobile-only {
  @media (min-width: 769px) {
    display: none !important;
  }
}

.desktop-only {
  @media (max-width: 768px) {
    display: none !important;
  }
}

.tablet-up {
  @media (max-width: 767px) {
    display: none !important;
  }
}

// Print styles
@media print {
  .no-print {
    display: none !important;
  }
  
  .page-header {
    border-bottom: 2px solid #000;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #000;
  }
}
