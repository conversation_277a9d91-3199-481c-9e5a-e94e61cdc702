// Import CSS reset
@import '@unocss/reset/tailwind.css';

// Import Element Plus theme customization
@import './element-plus.scss';

// Import custom variables
@import './variables.scss';

// Import component styles
@import './components.scss';

// Import responsive utilities
@import './responsive.scss';

// Global base styles
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  background-color: var(--color-bg-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

// Typography
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 1rem 0;
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-text-primary);
}

h1 { font-size: 2rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

// Links
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
  
  &:hover {
    color: var(--color-primary-dark);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

// Form elements
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

// Buttons
button {
  font-family: inherit;
  cursor: pointer;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// Images
img {
  max-width: 100%;
  height: auto;
}

// Lists
ul, ol {
  margin: 0 0 1rem 0;
  padding-left: 1.5rem;
}

li {
  margin-bottom: 0.25rem;
}

// Tables
table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  text-align: left;
  padding: 0.75rem;
  border-bottom: 1px solid var(--color-border);
}

th {
  font-weight: 600;
  background-color: var(--color-bg-secondary);
}

// Code
code, pre {
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
}

code {
  padding: 0.125rem 0.25rem;
  background-color: var(--color-bg-secondary);
  border-radius: 0.25rem;
}

pre {
  padding: 1rem;
  background-color: var(--color-bg-secondary);
  border-radius: 0.5rem;
  overflow-x: auto;
  
  code {
    padding: 0;
    background: none;
  }
}

// Utility classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

// Spacing utilities
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

// Display utilities
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

// Flex utilities
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }

// Position utilities
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

// Overflow utilities
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
  border-radius: 4px;
  
  &:hover {
    background: var(--color-text-secondary);
  }
}

// Focus styles
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// Selection styles
::selection {
  background-color: var(--color-primary-light);
  color: var(--color-text-primary);
}

// Print styles
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  .no-print {
    display: none !important;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  :root {
    --color-border: #000000;
    --color-text-primary: #000000;
    --color-text-secondary: #333333;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// Dark mode
@media (prefers-color-scheme: dark) {
  :root {
    --color-bg-page: #1a1a1a;
    --color-bg-primary: #2d2d2d;
    --color-bg-secondary: #3a3a3a;
    --color-text-primary: #ffffff;
    --color-text-secondary: #cccccc;
    --color-border: #4a4a4a;
    --color-border-dark: #666666;
  }
}

// Force dark mode when class is applied
.dark {
  --color-bg-page: #1a1a1a;
  --color-bg-primary: #2d2d2d;
  --color-bg-secondary: #3a3a3a;
  --color-text-primary: #ffffff;
  --color-text-secondary: #cccccc;
  --color-border: #4a4a4a;
  --color-border-dark: #666666;
}
