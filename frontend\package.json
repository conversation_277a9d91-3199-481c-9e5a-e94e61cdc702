{"name": "hdsc_query_app_frontend", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc && vite build", "preview": "vite preview --host", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "build:analyze": "vite build --mode analyze", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"vue": "^3.4.15", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.7", "@vueuse/core": "^10.7.2", "@vueuse/gesture": "^2.0.0", "element-plus": "^2.5.4", "@element-plus/icons-vue": "^2.3.1", "chart.js": "^4.4.1", "vue-chartjs": "^5.3.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^3.3.1", "vue3-touch-events": "^4.1.3", "viewport-units-buggyfill": "^0.6.2", "@unocss/reset": "^0.58.5", "unocss": "^0.58.5", "nprogress": "^0.2.0", "lodash-es": "^4.17.21", "dayjs": "^1.11.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.11", "@vitejs/plugin-vue-jsx": "^3.1.0", "vite": "^5.1.4", "typescript": "^5.3.3", "@types/node": "^20.11.19", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "vue-tsc": "^1.8.27", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint-plugin-vue": "^9.21.1", "vite-plugin-pwa": "^0.19.2", "vite-plugin-windicss": "^1.9.3", "windicss": "^3.5.6", "vitest": "^1.3.1", "@vitest/ui": "^1.3.1", "rollup-plugin-visualizer": "^5.12.0", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite-plugin-mock": "^3.0.1", "mockjs": "^1.1.0", "@types/mockjs": "^1.0.10"}}