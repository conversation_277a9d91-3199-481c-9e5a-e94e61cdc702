// Responsive design utilities and mixins

// Breakpoint mixins
@mixin mobile-only {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: 1280px) {
    @content;
  }
}

// Container queries (when supported)
@supports (container-type: inline-size) {
  .responsive-container {
    container-type: inline-size;
  }
  
  @container (max-width: 400px) {
    .container-responsive {
      flex-direction: column;
    }
  }
}

// Mobile-first responsive grid
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
  
  @include tablet-up {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  @include desktop-only {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  @include large-desktop {
    grid-template-columns: repeat(4, 1fr);
  }
}

// Responsive typography
.responsive-text {
  font-size: 1rem;
  line-height: 1.5;
  
  @include tablet-up {
    font-size: 1.125rem;
    line-height: 1.6;
  }
  
  @include desktop-only {
    font-size: 1.25rem;
    line-height: 1.7;
  }
}

.responsive-heading {
  font-size: 1.5rem;
  line-height: 1.3;
  
  @include tablet-up {
    font-size: 2rem;
  }
  
  @include desktop-only {
    font-size: 2.5rem;
  }
  
  @include large-desktop {
    font-size: 3rem;
  }
}

// Responsive spacing
.responsive-padding {
  padding: 1rem;
  
  @include tablet-up {
    padding: 1.5rem;
  }
  
  @include desktop-only {
    padding: 2rem;
  }
}

.responsive-margin {
  margin: 1rem 0;
  
  @include tablet-up {
    margin: 1.5rem 0;
  }
  
  @include desktop-only {
    margin: 2rem 0;
  }
}

// Mobile navigation
.mobile-nav {
  @include mobile-only {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--color-bg-primary);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.is-open {
      transform: translateX(0);
    }
  }
  
  @include tablet-up {
    position: static;
    transform: none;
    background: transparent;
  }
}

// Responsive tables
.responsive-table-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  
  @include mobile-only {
    margin: 0 -1rem;
    
    .el-table {
      min-width: 600px;
    }
  }
}

// Mobile-specific table styles
@include mobile-only {
  .mobile-table-card {
    display: block;
    
    .el-table {
      display: none;
    }
    
    .table-card-item {
      background: var(--color-bg-primary);
      border-radius: var(--radius-lg);
      padding: 1rem;
      margin-bottom: 0.75rem;
      box-shadow: var(--shadow-sm);
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--color-border-lighter);
        
        .card-title {
          font-weight: 600;
          color: var(--color-text-primary);
        }
        
        .card-actions {
          display: flex;
          gap: 0.5rem;
        }
      }
      
      .card-content {
        .card-field {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0;
          border-bottom: 1px solid var(--color-border-extra-light);
          
          &:last-child {
            border-bottom: none;
          }
          
          .field-label {
            color: var(--color-text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            min-width: 80px;
          }
          
          .field-value {
            color: var(--color-text-primary);
            font-size: 0.875rem;
            text-align: right;
            flex: 1;
          }
        }
      }
    }
  }
}

// Responsive images
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  
  &--contain {
    object-fit: contain;
  }
  
  &--cover {
    object-fit: cover;
  }
}

// Responsive video
.responsive-video {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; // 16:9 aspect ratio
  
  iframe,
  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// Touch-friendly elements
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  .el-button {
    min-height: 44px;
    padding: 12px 16px;
  }
  
  .el-input {
    .el-input__wrapper {
      min-height: 44px;
    }
  }
  
  // Increase touch targets for mobile
  .mobile-touch-target {
    padding: 12px;
    margin: 4px;
  }
}

// Landscape orientation on mobile
@media (max-height: 500px) and (orientation: landscape) {
  .landscape-compact {
    .page-header {
      margin-bottom: 1rem;
      
      .page-title {
        font-size: 1.5rem;
      }
    }
    
    .card {
      padding: 1rem;
    }
    
    .responsive-padding {
      padding: 0.75rem;
    }
  }
}

// High DPI displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  // Sharper borders on high DPI
  .sharp-border {
    border-width: 0.5px;
  }
}

// Responsive utilities
.show-mobile {
  @include tablet-up {
    display: none !important;
  }
}

.hide-mobile {
  @include mobile-only {
    display: none !important;
  }
}

.show-tablet {
  @include mobile-only {
    display: none !important;
  }
  
  @include desktop-only {
    display: none !important;
  }
}

.show-desktop {
  @media (max-width: 1023px) {
    display: none !important;
  }
}

// Responsive flexbox utilities
.flex-mobile-column {
  @include mobile-only {
    flex-direction: column;
  }
}

.flex-tablet-row {
  @include tablet-up {
    flex-direction: row;
  }
}

// Responsive text alignment
.text-center-mobile {
  @include mobile-only {
    text-align: center;
  }
}

.text-left-desktop {
  @include desktop-only {
    text-align: left;
  }
}

// Responsive widths
.full-width-mobile {
  @include mobile-only {
    width: 100%;
  }
}

.auto-width-desktop {
  @include desktop-only {
    width: auto;
  }
}

// Print-specific responsive styles
@media print {
  .responsive-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
  
  .hide-print {
    display: none !important;
  }
  
  .responsive-padding {
    padding: 0.5rem !important;
  }
}
