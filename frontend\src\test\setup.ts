// Test setup file for Vitest

import { vi } from 'vitest'
import { config } from '@vue/test-utils'
import ElementPlus from 'element-plus'

// Mock Element Plus globally
config.global.plugins = [ElementPlus]

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn().mockImplementation(cb => {
  setTimeout(cb, 0)
  return 1
})

global.cancelAnimationFrame = vi.fn()

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.sessionStorage = sessionStorageMock

// Mock fetch
global.fetch = vi.fn()

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mocked-url')
global.URL.revokeObjectURL = vi.fn()

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}

// Mock environment variables
vi.mock('import.meta.env', () => ({
  VITE_API_BASE_URL: 'http://localhost:5000/api',
  VITE_APP_TITLE: '太享查询系统',
  VITE_APP_VERSION: '2.0.0',
  VITE_APP_ENV: 'test',
  VITE_DEBUG: 'false',
  VITE_MOCK_API: 'true'
}))

// Mock router
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  }),
  useRoute: () => ({
    path: '/',
    name: 'Home',
    params: {},
    query: {},
    meta: {},
  }),
  createRouter: vi.fn(),
  createWebHistory: vi.fn(),
}))

// Mock Pinia stores
vi.mock('@/stores/app', () => ({
  useAppStore: () => ({
    isLoading: false,
    isMobile: false,
    isTablet: false,
    sidebarCollapsed: false,
    mobileMenuOpen: false,
    theme: 'light',
    viewport: {
      width: 1024,
      height: 768,
      orientation: 'landscape'
    },
    setLoading: vi.fn(),
    toggleSidebar: vi.fn(),
    toggleTheme: vi.fn(),
    updateViewport: vi.fn(),
  }),
}))

vi.mock('@/stores/user', () => ({
  useUserStore: () => ({
    user: null,
    token: null,
    isAuthenticated: false,
    permissions: [],
    login: vi.fn(),
    logout: vi.fn(),
    hasPermission: vi.fn(() => true),
    canAccess: vi.fn(() => true),
  }),
}))

// Mock API client
vi.mock('@/utils/api', () => ({
  ApiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    upload: vi.fn(),
    download: vi.fn(),
  },
  api: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    defaults: {
      headers: {
        common: {}
      }
    },
    interceptors: {
      request: {
        use: vi.fn()
      },
      response: {
        use: vi.fn()
      }
    }
  }
}))

// Mock Element Plus message
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
    },
    ElMessageBox: {
      confirm: vi.fn(),
      alert: vi.fn(),
      prompt: vi.fn(),
    },
    ElNotification: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
    }
  }
})

// Mock Chart.js
vi.mock('chart.js', () => ({
  Chart: vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
    update: vi.fn(),
    render: vi.fn(),
  })),
  registerables: [],
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => '2024-01-01'),
  formatDistanceToNow: vi.fn(() => '1 hour ago'),
  isValid: vi.fn(() => true),
  parseISO: vi.fn((dateStr) => new Date(dateStr)),
}))

// Mock lodash-es
vi.mock('lodash-es', () => ({
  debounce: vi.fn((fn) => fn),
  throttle: vi.fn((fn) => fn),
  cloneDeep: vi.fn((obj) => JSON.parse(JSON.stringify(obj))),
  merge: vi.fn((target, source) => ({ ...target, ...source })),
  isEmpty: vi.fn((value) => !value || Object.keys(value).length === 0),
  isEqual: vi.fn((a, b) => JSON.stringify(a) === JSON.stringify(b)),
}))

// Setup global test utilities
global.nextTick = () => new Promise(resolve => setTimeout(resolve, 0))

// Mock CSS modules
vi.mock('*.module.css', () => ({}))
vi.mock('*.module.scss', () => ({}))

// Mock image imports
vi.mock('*.png', () => 'mocked-image.png')
vi.mock('*.jpg', () => 'mocked-image.jpg')
vi.mock('*.jpeg', () => 'mocked-image.jpeg')
vi.mock('*.gif', () => 'mocked-image.gif')
vi.mock('*.svg', () => 'mocked-image.svg')

// Setup test environment
beforeEach(() => {
  // Clear all mocks before each test
  vi.clearAllMocks()
  
  // Reset localStorage
  localStorageMock.getItem.mockClear()
  localStorageMock.setItem.mockClear()
  localStorageMock.removeItem.mockClear()
  localStorageMock.clear.mockClear()
  
  // Reset sessionStorage
  sessionStorageMock.getItem.mockClear()
  sessionStorageMock.setItem.mockClear()
  sessionStorageMock.removeItem.mockClear()
  sessionStorageMock.clear.mockClear()
})

afterEach(() => {
  // Cleanup after each test
  vi.restoreAllMocks()
})

// Global test helpers
global.createMockResponse = (data: any, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
})

global.createMockError = (message: string, status = 500) => ({
  message,
  response: {
    data: { message },
    status,
    statusText: 'Internal Server Error',
  },
})

// Export test utilities
export {
  localStorageMock,
  sessionStorageMock,
}
