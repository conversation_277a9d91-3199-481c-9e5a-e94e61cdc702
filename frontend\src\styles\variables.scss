// CSS Custom Properties (CSS Variables)
:root {
  // Colors - Primary Palette
  --color-primary: #409eff;
  --color-primary-light: #79bbff;
  --color-primary-dark: #337ecc;
  --color-primary-lighter: #a0cfff;
  --color-primary-darker: #2b73b3;
  
  // Colors - Secondary Palette
  --color-success: #67c23a;
  --color-success-light: #85ce61;
  --color-success-dark: #529b2e;
  
  --color-warning: #e6a23c;
  --color-warning-light: #ebb563;
  --color-warning-dark: #b88230;
  
  --color-danger: #f56c6c;
  --color-danger-light: #f78989;
  --color-danger-dark: #c45656;
  
  --color-info: #909399;
  --color-info-light: #a6a9ad;
  --color-info-dark: #73767a;
  
  // Colors - Neutral Palette
  --color-white: #ffffff;
  --color-black: #000000;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  // Background Colors
  --color-bg-page: #f5f7fa;
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8f9fa;
  --color-bg-tertiary: #e9ecef;
  
  // Text Colors
  --color-text-primary: #303133;
  --color-text-regular: #606266;
  --color-text-secondary: #909399;
  --color-text-placeholder: #c0c4cc;
  --color-text-disabled: #c0c4cc;
  
  // Border Colors
  --color-border: #dcdfe6;
  --color-border-light: #e4e7ed;
  --color-border-lighter: #ebeef5;
  --color-border-extra-light: #f2f6fc;
  --color-border-dark: #d4d7de;
  --color-border-darker: #cdd0d6;
  
  // Typography
  --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Source Code Pro', monospace;
  
  --font-size-xs: 0.75rem;    // 12px
  --font-size-sm: 0.875rem;   // 14px
  --font-size-base: 1rem;     // 16px
  --font-size-lg: 1.125rem;   // 18px
  --font-size-xl: 1.25rem;    // 20px
  --font-size-2xl: 1.5rem;    // 24px
  --font-size-3xl: 1.875rem;  // 30px
  --font-size-4xl: 2.25rem;   // 36px
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  // Spacing
  --spacing-xs: 0.25rem;   // 4px
  --spacing-sm: 0.5rem;    // 8px
  --spacing-md: 0.75rem;   // 12px
  --spacing-lg: 1rem;      // 16px
  --spacing-xl: 1.5rem;    // 24px
  --spacing-2xl: 2rem;     // 32px
  --spacing-3xl: 3rem;     // 48px
  --spacing-4xl: 4rem;     // 64px
  
  // Border Radius
  --radius-none: 0;
  --radius-sm: 0.125rem;   // 2px
  --radius-base: 0.25rem;  // 4px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-full: 9999px;
  
  // Shadows
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  // Z-index
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  // Transitions
  --transition-fast: 150ms ease;
  --transition-base: 250ms ease;
  --transition-slow: 350ms ease;
  
  --transition-all: all var(--transition-base);
  --transition-colors: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
  --transition-opacity: opacity var(--transition-fast);
  --transition-transform: transform var(--transition-base);
  
  // Layout
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  
  --sidebar-width: 16rem;      // 256px
  --sidebar-width-collapsed: 4rem; // 64px
  --header-height: 4rem;       // 64px
  --header-height-mobile: 3.5rem; // 56px
  
  // Breakpoints (for use in JavaScript)
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  // Safe area insets (for notched devices)
  --safe-area-top: env(safe-area-inset-top, 0px);
  --safe-area-right: env(safe-area-inset-right, 0px);
  --safe-area-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-left: env(safe-area-inset-left, 0px);
  
  // Viewport units fix for mobile
  --vh: 1vh;
  --vw: 1vw;
}

// SCSS Variables (for use in SCSS files)
$colors: (
  'primary': var(--color-primary),
  'success': var(--color-success),
  'warning': var(--color-warning),
  'danger': var(--color-danger),
  'info': var(--color-info),
  'white': var(--color-white),
  'black': var(--color-black)
);

$grays: (
  '50': var(--color-gray-50),
  '100': var(--color-gray-100),
  '200': var(--color-gray-200),
  '300': var(--color-gray-300),
  '400': var(--color-gray-400),
  '500': var(--color-gray-500),
  '600': var(--color-gray-600),
  '700': var(--color-gray-700),
  '800': var(--color-gray-800),
  '900': var(--color-gray-900)
);

$font-sizes: (
  'xs': var(--font-size-xs),
  'sm': var(--font-size-sm),
  'base': var(--font-size-base),
  'lg': var(--font-size-lg),
  'xl': var(--font-size-xl),
  '2xl': var(--font-size-2xl),
  '3xl': var(--font-size-3xl),
  '4xl': var(--font-size-4xl)
);

$spacings: (
  'xs': var(--spacing-xs),
  'sm': var(--spacing-sm),
  'md': var(--spacing-md),
  'lg': var(--spacing-lg),
  'xl': var(--spacing-xl),
  '2xl': var(--spacing-2xl),
  '3xl': var(--spacing-3xl),
  '4xl': var(--spacing-4xl)
);

$breakpoints: (
  'sm': 640px,
  'md': 768px,
  'lg': 1024px,
  'xl': 1280px,
  '2xl': 1536px
);

// Mixins
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin respond-below($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
