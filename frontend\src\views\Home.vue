<template>
  <div class="home-page">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon class="title-icon"><House /></el-icon>
          首页
        </h1>
        <p class="page-subtitle">欢迎使用太享查询系统</p>
      </div>
      <div class="header-actions">
        <el-badge :value="stats.todayOrders" :hidden="stats.todayOrders === 0">
          <el-button type="primary" @click="navigateToQuery">
            <el-icon><Search /></el-icon>
            快速查询
          </el-button>
        </el-badge>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <StatCard
        v-for="stat in statisticsCards"
        :key="stat.key"
        :title="stat.title"
        :value="stat.value"
        :icon="stat.icon"
        :color="stat.color"
        :trend="stat.trend"
        :loading="statsLoading"
        @click="handleStatClick(stat)"
      />
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><Operation /></el-icon>
          常用功能
        </h2>
      </div>
      
      <div class="actions-grid">
        <ActionCard
          v-for="action in quickActions"
          :key="action.key"
          :title="action.title"
          :description="action.description"
          :icon="action.icon"
          :color="action.color"
          :disabled="!userStore.canAccess(action.permission)"
          @click="handleActionClick(action)"
        />
      </div>
    </div>

    <!-- Recent Activity & Notifications -->
    <div class="content-grid">
      <!-- Recent Activity -->
      <div class="activity-section">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon><Clock /></el-icon>
            最近活动
          </h3>
          <el-button text @click="viewAllActivity">
            查看全部
          </el-button>
        </div>
        
        <div class="activity-list">
          <div
            v-for="activity in recentActivity"
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon" :class="`activity-${activity.type}`">
              <el-icon>
                <component :is="getActivityIcon(activity.type)" />
              </el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
            </div>
          </div>
          
          <div v-if="recentActivity.length === 0" class="empty-state">
            <el-empty description="暂无活动记录" :image-size="80" />
          </div>
        </div>
      </div>

      <!-- System Status -->
      <div class="status-section">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon><Monitor /></el-icon>
            系统状态
          </h3>
          <el-button
            text
            :loading="statusLoading"
            @click="refreshStatus"
          >
            刷新
          </el-button>
        </div>
        
        <div class="status-list">
          <div
            v-for="status in systemStatus"
            :key="status.key"
            class="status-item"
          >
            <div class="status-indicator">
              <el-icon
                :class="[
                  'status-icon',
                  `status-${status.status}`
                ]"
              >
                <component :is="getStatusIcon(status.status)" />
              </el-icon>
            </div>
            <div class="status-content">
              <div class="status-name">{{ status.name }}</div>
              <div class="status-value">{{ status.value }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { ApiClient } from '@/utils/api'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import StatCard from '@/components/home/<USER>'
import ActionCard from '@/components/home/<USER>'
import {
  House,
  Search,
  Operation,
  Clock,
  Monitor,
  User,
  Document,
  PieChart,
  Warning,
  Calendar,
  Tools,
  CircleCheck,
  CircleClose,
  QuestionFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// Reactive data
const stats = ref({
  todayOrders: 0,
  overdueOrders: 0,
  totalCustomers: 0,
  systemStatus: 'normal'
})

const statsLoading = ref(false)
const statusLoading = ref(false)
const recentActivity = ref([])
const systemStatus = ref([
  { key: 'api', name: 'API服务', status: 'online', value: '正常' },
  { key: 'database', name: '数据库', status: 'online', value: '连接正常' },
  { key: 'cache', name: '缓存系统', status: 'online', value: '运行中' }
])

// Computed properties
const statisticsCards = computed(() => [
  {
    key: 'today',
    title: '今日订单',
    value: stats.value.todayOrders,
    icon: 'Calendar',
    color: 'primary',
    trend: { value: 12, type: 'up' }
  },
  {
    key: 'overdue',
    title: '逾期订单',
    value: stats.value.overdueOrders,
    icon: 'Warning',
    color: 'warning',
    trend: { value: 5, type: 'down' }
  },
  {
    key: 'customers',
    title: '客户总数',
    value: stats.value.totalCustomers,
    icon: 'User',
    color: 'success',
    trend: { value: 8, type: 'up' }
  },
  {
    key: 'system',
    title: '系统状态',
    value: stats.value.systemStatus === 'normal' ? '正常' : '异常',
    icon: 'Monitor',
    color: stats.value.systemStatus === 'normal' ? 'success' : 'danger'
  }
])

const quickActions = computed(() => [
  {
    key: 'query',
    title: '数据查询',
    description: '按日期筛选查询订单数据',
    icon: 'Search',
    color: 'primary',
    permission: 'limited'
  },
  {
    key: 'overdue',
    title: '逾期订单',
    description: '查看和管理逾期订单',
    icon: 'Warning',
    color: 'warning',
    permission: 'limited'
  },
  {
    key: 'customer',
    title: '客户汇总',
    description: '查看客户订单汇总信息',
    icon: 'User',
    color: 'info',
    permission: 'standard'
  },
  {
    key: 'summary',
    title: '数据汇总',
    description: '查看数据统计和图表分析',
    icon: 'PieChart',
    color: 'success',
    permission: 'full'
  },
  {
    key: 'contract',
    title: '合同生成',
    description: '生成和下载合同文档',
    icon: 'Document',
    color: 'purple',
    permission: 'standard'
  },
  {
    key: 'tools',
    title: '实用工具',
    description: '计算器、二维码等工具',
    icon: 'Tools',
    color: 'secondary',
    permission: 'limited'
  }
])

// Lifecycle
onMounted(() => {
  loadDashboardData()
})

// Methods
async function loadDashboardData() {
  try {
    statsLoading.value = true
    
    const [statsData, activityData] = await Promise.all([
      ApiClient.get('/dashboard/stats'),
      ApiClient.get('/dashboard/activity')
    ])
    
    stats.value = statsData
    recentActivity.value = activityData
    
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
    ElMessage.error('加载数据失败')
  } finally {
    statsLoading.value = false
  }
}

async function refreshStatus() {
  try {
    statusLoading.value = true
    const statusData = await ApiClient.get('/system/status')
    systemStatus.value = statusData
  } catch (error) {
    console.error('Failed to refresh status:', error)
  } finally {
    statusLoading.value = false
  }
}

function handleStatClick(stat: any) {
  switch (stat.key) {
    case 'today':
      router.push({ name: 'DataQuery', query: { date: 'today' } })
      break
    case 'overdue':
      router.push({ name: 'OverdueOrders' })
      break
    case 'customers':
      router.push({ name: 'CustomerSummary' })
      break
  }
}

function handleActionClick(action: any) {
  switch (action.key) {
    case 'query':
      router.push({ name: 'DataQuery' })
      break
    case 'overdue':
      router.push({ name: 'OverdueOrders' })
      break
    case 'customer':
      router.push({ name: 'CustomerSummary' })
      break
    case 'summary':
      router.push({ name: 'DataSummary' })
      break
    case 'contract':
      router.push({ name: 'ContractGenerator' })
      break
    case 'tools':
      // Open tools modal or navigate to tools page
      ElMessage.info('工具功能开发中...')
      break
  }
}

function navigateToQuery() {
  router.push({ name: 'DataQuery' })
}

function viewAllActivity() {
  // Navigate to activity page or open modal
  ElMessage.info('活动记录功能开发中...')
}

function getActivityIcon(type: string) {
  const icons = {
    query: Search,
    export: Document,
    login: User,
    system: Monitor
  }
  return icons[type] || QuestionFilled
}

function getStatusIcon(status: string) {
  const icons = {
    online: CircleCheck,
    offline: CircleClose,
    warning: Warning
  }
  return icons[status] || QuestionFilled
}

function formatTime(date: string) {
  return formatDistanceToNow(new Date(date), {
    addSuffix: true,
    locale: zhCN
  })
}
</script>

<style lang="scss" scoped>
.home-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }
    
    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0 0 0.5rem 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--color-text-primary);
        
        .title-icon {
          color: var(--color-primary);
        }
      }
      
      .page-subtitle {
        margin: 0;
        color: var(--color-text-secondary);
        font-size: 1.125rem;
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }
  
  .quick-actions-section {
    margin-bottom: 2rem;
    
    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
    }
  }
  
  .content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    
    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }
  
  .activity-section,
  .status-section {
    background: var(--color-bg-primary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
  }
  
  .activity-list {
    .activity-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 0;
      border-bottom: 1px solid var(--color-border-lighter);
      
      &:last-child {
        border-bottom: none;
      }
      
      .activity-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.activity-query {
          background: var(--color-primary-lighter);
          color: var(--color-primary);
        }
        
        &.activity-export {
          background: var(--color-success-light);
          color: var(--color-success);
        }
        
        &.activity-login {
          background: var(--color-info-light);
          color: var(--color-info);
        }
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-weight: 500;
          color: var(--color-text-primary);
          margin-bottom: 0.25rem;
        }
        
        .activity-time {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
        }
      }
    }
  }
  
  .status-list {
    .status-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 0;
      border-bottom: 1px solid var(--color-border-lighter);
      
      &:last-child {
        border-bottom: none;
      }
      
      .status-indicator {
        .status-icon {
          font-size: 1.25rem;
          
          &.status-online {
            color: var(--color-success);
          }
          
          &.status-offline {
            color: var(--color-danger);
          }
          
          &.status-warning {
            color: var(--color-warning);
          }
        }
      }
      
      .status-content {
        flex: 1;
        
        .status-name {
          font-weight: 500;
          color: var(--color-text-primary);
          margin-bottom: 0.25rem;
        }
        
        .status-value {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
        }
      }
    }
  }
  
  .empty-state {
    padding: 2rem 0;
    text-align: center;
  }
}
</style>
