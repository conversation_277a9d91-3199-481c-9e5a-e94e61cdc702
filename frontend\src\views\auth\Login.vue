<template>
  <div class="login-page">
    <div class="login-container">
      <!-- Background decoration -->
      <div class="login-background">
        <div class="bg-pattern"></div>
        <div class="bg-gradient"></div>
      </div>
      
      <!-- Login form -->
      <div class="login-form-wrapper">
        <div class="login-header">
          <div class="logo-section">
            <img
              src="/logo.png"
              alt="Logo"
              class="logo"
              @error="handleLogoError"
            >
            <h1 class="app-title">太享查询系统</h1>
          </div>
          <p class="app-subtitle">金融数据查询展示系统</p>
        </div>
        
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              size="large"
              :prefix-icon="User"
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              :prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item>
            <div class="form-options">
              <el-checkbox v-model="loginForm.remember">
                记住我
              </el-checkbox>
              <el-link type="primary" :underline="false">
                忘记密码？
              </el-link>
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              <span v-if="!loading">登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>
        
        <!-- Additional info -->
        <div class="login-footer">
          <div class="version-info">
            <span>版本 {{ appVersion }}</span>
          </div>
          <div class="support-info">
            <el-link type="info" :underline="false">
              技术支持
            </el-link>
            <span class="divider">|</span>
            <el-link type="info" :underline="false">
              使用帮助
            </el-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import type { FormInstance, FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const appStore = useAppStore()

const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const appVersion = import.meta.env.VITE_APP_VERSION || '2.0.0'

// Form data
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// Form validation rules
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

onMounted(() => {
  // Auto focus on username input
  nextTick(() => {
    const usernameInput = document.querySelector('input[placeholder="请输入用户名"]') as HTMLInputElement
    if (usernameInput) {
      usernameInput.focus()
    }
  })
  
  // Load saved username if remember me was checked
  const savedUsername = localStorage.getItem('saved-username')
  if (savedUsername) {
    loginForm.username = savedUsername
    loginForm.remember = true
  }
})

// Handle login
async function handleLogin() {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    const result = await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    })
    
    if (result.success) {
      // Save username if remember me is checked
      if (loginForm.remember) {
        localStorage.setItem('saved-username', loginForm.username)
      } else {
        localStorage.removeItem('saved-username')
      }
      
      ElMessage.success('登录成功')
      
      // Redirect to intended page or home
      const redirectPath = (route.query.redirect as string) || '/'
      await router.push(redirectPath)
    } else {
      ElMessage.error(result.message || '登录失败')
    }
  } catch (error) {
    console.error('Login error:', error)
    ElMessage.error('登录过程中发生错误，请重试')
  } finally {
    loading.value = false
  }
}

// Handle logo error
function handleLogoError(event: Event) {
  const img = event.target as HTMLImageElement
  img.src = '/default-logo.png'
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  min-height: calc(var(--vh, 1vh) * 100);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.login-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  z-index: 2;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  
  .bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px, 60px 60px, 30px 30px;
    background-position: 0 0, 25px 25px, 15px 15px;
    animation: float 20s ease-in-out infinite;
  }
  
  .bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  }
}

.login-form-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  @media (max-width: 480px) {
    padding: 2rem 1.5rem;
    border-radius: var(--radius-xl);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
  
  .logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1rem;
    
    .logo {
      width: 64px;
      height: 64px;
      margin-bottom: 1rem;
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-md);
    }
    
    .app-title {
      margin: 0;
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--color-text-primary);
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
  
  .app-subtitle {
    margin: 0;
    color: var(--color-text-secondary);
    font-size: 0.875rem;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 1.5rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    
    .el-checkbox {
      :deep(.el-checkbox__label) {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
      }
    }
    
    .el-link {
      font-size: 0.875rem;
    }
  }
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    transition: var(--transition-all);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

.login-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-border-lighter);
  
  .version-info {
    text-align: center;
    margin-bottom: 1rem;
    
    span {
      font-size: 0.75rem;
      color: var(--color-text-placeholder);
    }
  }
  
  .support-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    
    .el-link {
      font-size: 0.75rem;
    }
    
    .divider {
      color: var(--color-text-placeholder);
      font-size: 0.75rem;
    }
  }
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Mobile optimizations
@media (max-width: 480px) {
  .login-page {
    padding: 0.5rem;
  }
  
  .login-header {
    .logo-section {
      .logo {
        width: 48px;
        height: 48px;
      }
      
      .app-title {
        font-size: 1.5rem;
      }
    }
  }
  
  .login-form {
    .form-options {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }
  }
}

// Dark mode support
.dark {
  .login-form-wrapper {
    background: rgba(45, 45, 45, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .login-form-wrapper {
    background: white;
    border: 2px solid #000;
  }
  
  .login-button {
    background: #000 !important;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .bg-pattern {
    animation: none;
  }
  
  .login-button {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}
</style>
