<template>
  <span ref="countUpRef">
    <slot :value="displayValue">{{ displayValue }}</slot>
  </span>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

interface Props {
  startVal?: number
  endVal: number
  duration?: number
  autoplay?: boolean
  decimals?: number
  decimal?: string
  separator?: string
  prefix?: string
  suffix?: string
  useEasing?: boolean
  useGrouping?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  startVal: 0,
  duration: 2,
  autoplay: true,
  decimals: 0,
  decimal: '.',
  separator: ',',
  prefix: '',
  suffix: '',
  useEasing: true,
  useGrouping: true
})

const countUpRef = ref<HTMLElement>()
const displayValue = ref(props.startVal)

let animationId: number | null = null
let startTime: number | null = null

// Easing function
function easeOutExpo(t: number): number {
  return t === 1 ? 1 : 1 - Math.pow(2, -10 * t)
}

// Format number with separators
function formatNumber(num: number): string {
  const fixedNum = num.toFixed(props.decimals)
  const parts = fixedNum.split('.')
  
  if (props.useGrouping) {
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, props.separator)
  }
  
  const formattedNum = parts.join(props.decimal)
  return props.prefix + formattedNum + props.suffix
}

// Animation function
function animate(currentTime: number) {
  if (startTime === null) {
    startTime = currentTime
  }
  
  const elapsed = currentTime - startTime
  const progress = Math.min(elapsed / (props.duration * 1000), 1)
  
  let easedProgress = progress
  if (props.useEasing) {
    easedProgress = easeOutExpo(progress)
  }
  
  const currentValue = props.startVal + (props.endVal - props.startVal) * easedProgress
  displayValue.value = formatNumber(currentValue)
  
  if (progress < 1) {
    animationId = requestAnimationFrame(animate)
  } else {
    displayValue.value = formatNumber(props.endVal)
    animationId = null
    startTime = null
  }
}

// Start animation
function start() {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  
  startTime = null
  displayValue.value = formatNumber(props.startVal)
  animationId = requestAnimationFrame(animate)
}

// Stop animation
function stop() {
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
    startTime = null
  }
}

// Reset to start value
function reset() {
  stop()
  displayValue.value = formatNumber(props.startVal)
}

// Watch for endVal changes
watch(() => props.endVal, () => {
  if (props.autoplay) {
    start()
  }
})

onMounted(() => {
  if (props.autoplay) {
    start()
  } else {
    displayValue.value = formatNumber(props.endVal)
  }
})

// Expose methods
defineExpose({
  start,
  stop,
  reset
})
</script>
