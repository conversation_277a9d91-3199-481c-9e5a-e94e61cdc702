<template>
  <div class="notification-container">
    <!-- Toast Notifications -->
    <TransitionGroup
      name="toast"
      tag="div"
      class="toast-list"
    >
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="toast-notification"
        :class="[
          `toast-${notification.type}`,
          { 'toast-mobile': appStore.isMobile }
        ]"
        @click="removeNotification(notification.id)"
      >
        <div class="toast-icon">
          <el-icon>
            <SuccessFilled v-if="notification.type === 'success'" />
            <WarningFilled v-else-if="notification.type === 'warning'" />
            <CircleCloseFilled v-else-if="notification.type === 'error'" />
            <InfoFilled v-else />
          </el-icon>
        </div>
        
        <div class="toast-content">
          <div class="toast-title">{{ notification.title }}</div>
          <div v-if="notification.message" class="toast-message">
            {{ notification.message }}
          </div>
        </div>
        
        <button
          class="toast-close"
          @click.stop="removeNotification(notification.id)"
        >
          <el-icon><Close /></el-icon>
        </button>
      </div>
    </TransitionGroup>
    
    <!-- System Notifications -->
    <div v-if="systemNotifications.length > 0" class="system-notifications">
      <div
        v-for="notification in systemNotifications"
        :key="notification.id"
        class="system-notification"
        :class="`system-${notification.priority}`"
      >
        <div class="system-content">
          <div class="system-title">
            <el-icon class="system-icon">
              <Bell />
            </el-icon>
            {{ notification.title }}
          </div>
          <div class="system-message">{{ notification.message }}</div>
          <div class="system-time">{{ formatTime(notification.createdAt) }}</div>
        </div>
        
        <div class="system-actions">
          <el-button
            size="small"
            type="primary"
            @click="handleSystemNotificationAction(notification)"
          >
            查看
          </el-button>
          <el-button
            size="small"
            @click="dismissSystemNotification(notification.id)"
          >
            忽略
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  InfoFilled,
  Close,
  Bell
} from '@element-plus/icons-vue'

interface ToastNotification {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
}

interface SystemNotification {
  id: string
  title: string
  message: string
  priority: 'low' | 'medium' | 'high'
  createdAt: Date
  action?: {
    label: string
    handler: () => void
  }
}

const appStore = useAppStore()
const notifications = ref<ToastNotification[]>([])
const systemNotifications = ref<SystemNotification[]>([])

let notificationId = 0

onMounted(() => {
  // Listen for global notification events
  window.addEventListener('app:notify', handleGlobalNotification)
  window.addEventListener('app:system-notify', handleSystemNotification)
  
  // Check for system notifications
  checkSystemNotifications()
})

onUnmounted(() => {
  window.removeEventListener('app:notify', handleGlobalNotification)
  window.removeEventListener('app:system-notify', handleSystemNotification)
})

// Toast notification methods
function addNotification(notification: Omit<ToastNotification, 'id'>) {
  const id = `notification-${++notificationId}`
  const newNotification: ToastNotification = {
    id,
    duration: 4000,
    ...notification
  }
  
  notifications.value.push(newNotification)
  
  // Auto remove after duration
  if (!newNotification.persistent && newNotification.duration) {
    setTimeout(() => {
      removeNotification(id)
    }, newNotification.duration)
  }
  
  // Limit number of notifications
  if (notifications.value.length > 5) {
    notifications.value.shift()
  }
}

function removeNotification(id: string) {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

function clearAllNotifications() {
  notifications.value = []
}

// System notification methods
function addSystemNotification(notification: Omit<SystemNotification, 'id' | 'createdAt'>) {
  const id = `system-${++notificationId}`
  const newNotification: SystemNotification = {
    id,
    createdAt: new Date(),
    ...notification
  }
  
  systemNotifications.value.push(newNotification)
  
  // Limit number of system notifications
  if (systemNotifications.value.length > 3) {
    systemNotifications.value.shift()
  }
}

function dismissSystemNotification(id: string) {
  const index = systemNotifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    systemNotifications.value.splice(index, 1)
  }
}

function handleSystemNotificationAction(notification: SystemNotification) {
  if (notification.action) {
    notification.action.handler()
  }
  dismissSystemNotification(notification.id)
}

// Event handlers
function handleGlobalNotification(event: CustomEvent) {
  addNotification(event.detail)
}

function handleSystemNotification(event: CustomEvent) {
  addSystemNotification(event.detail)
}

// Check for system notifications
async function checkSystemNotifications() {
  try {
    // Check for app updates
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration && registration.waiting) {
        addSystemNotification({
          title: '应用更新',
          message: '发现新版本，点击更新以获得最新功能',
          priority: 'medium',
          action: {
            label: '立即更新',
            handler: () => {
              registration.waiting?.postMessage({ type: 'SKIP_WAITING' })
              window.location.reload()
            }
          }
        })
      }
    }
    
    // Check for maintenance notifications
    // This would typically come from your API
    
  } catch (error) {
    console.error('Failed to check system notifications:', error)
  }
}

// Utility functions
function formatTime(date: Date): string {
  return formatDistanceToNow(date, {
    addSuffix: true,
    locale: zhCN
  })
}

// Expose methods for global use
defineExpose({
  addNotification,
  removeNotification,
  clearAllNotifications,
  addSystemNotification
})
</script>

<style lang="scss" scoped>
.notification-container {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9999;
  pointer-events: none;
  
  .toast-list {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 400px;
    
    @media (max-width: 768px) {
      top: 10px;
      right: 10px;
      left: 10px;
      max-width: none;
    }
  }
  
  .toast-notification {
    pointer-events: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 16px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid;
    
    &:hover {
      transform: translateX(-4px);
      box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
    }
    
    &.toast-success {
      border-left-color: #67c23a;
      
      .toast-icon {
        color: #67c23a;
      }
    }
    
    &.toast-warning {
      border-left-color: #e6a23c;
      
      .toast-icon {
        color: #e6a23c;
      }
    }
    
    &.toast-error {
      border-left-color: #f56c6c;
      
      .toast-icon {
        color: #f56c6c;
      }
    }
    
    &.toast-info {
      border-left-color: #409eff;
      
      .toast-icon {
        color: #409eff;
      }
    }
    
    &.toast-mobile {
      padding: 12px;
      font-size: 14px;
    }
    
    .toast-icon {
      font-size: 20px;
      flex-shrink: 0;
      margin-top: 2px;
    }
    
    .toast-content {
      flex: 1;
      min-width: 0;
      
      .toast-title {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
        line-height: 1.4;
      }
      
      .toast-message {
        color: #606266;
        font-size: 14px;
        line-height: 1.4;
      }
    }
    
    .toast-close {
      background: none;
      border: none;
      color: #909399;
      cursor: pointer;
      padding: 0;
      font-size: 16px;
      flex-shrink: 0;
      
      &:hover {
        color: #606266;
      }
    }
  }
  
  .system-notifications {
    position: fixed;
    bottom: 20px;
    right: 20px;
    max-width: 350px;
    
    @media (max-width: 768px) {
      bottom: 10px;
      right: 10px;
      left: 10px;
      max-width: none;
    }
    
    .system-notification {
      pointer-events: auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      padding: 16px;
      margin-bottom: 12px;
      border-left: 4px solid;
      
      &.system-low {
        border-left-color: #909399;
      }
      
      &.system-medium {
        border-left-color: #e6a23c;
      }
      
      &.system-high {
        border-left-color: #f56c6c;
      }
      
      .system-content {
        margin-bottom: 12px;
        
        .system-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 8px;
          
          .system-icon {
            color: #409eff;
          }
        }
        
        .system-message {
          color: #606266;
          font-size: 14px;
          line-height: 1.4;
          margin-bottom: 8px;
        }
        
        .system-time {
          color: #909399;
          font-size: 12px;
        }
      }
      
      .system-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
      }
    }
  }
}

// Transitions
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}
</style>
