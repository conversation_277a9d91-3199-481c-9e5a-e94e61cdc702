<template>
  <div class="data-query-page">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon class="title-icon"><Search /></el-icon>
          数据查询
        </h1>
        <p class="page-subtitle">按条件筛选查询订单数据</p>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          :icon="Download"
          :loading="exporting"
          @click="handleExport"
        >
          导出数据
        </el-button>
      </div>
    </div>

    <!-- Search Form -->
    <div class="search-section">
      <el-card class="search-card">
        <template #header>
          <div class="card-header">
            <span>筛选条件</span>
            <el-button
              text
              :icon="expanded ? ArrowUp : ArrowDown"
              @click="expanded = !expanded"
            >
              {{ expanded ? '收起' : '展开' }}
            </el-button>
          </div>
        </template>
        
        <el-collapse-transition>
          <div v-show="expanded">
            <el-form
              ref="searchFormRef"
              :model="searchForm"
              :inline="!appStore.isMobile"
              class="search-form"
              @submit.prevent="handleSearch"
            >
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="searchForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :size="appStore.isMobile ? 'default' : 'large'"
                  class="date-picker"
                />
              </el-form-item>
              
              <el-form-item label="客户姓名">
                <el-input
                  v-model="searchForm.customerName"
                  placeholder="请输入客户姓名"
                  clearable
                  :size="appStore.isMobile ? 'default' : 'large'"
                />
              </el-form-item>
              
              <el-form-item label="手机号码">
                <el-input
                  v-model="searchForm.phone"
                  placeholder="请输入手机号码"
                  clearable
                  :size="appStore.isMobile ? 'default' : 'large'"
                />
              </el-form-item>
              
              <el-form-item label="订单状态">
                <el-select
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  :size="appStore.isMobile ? 'default' : 'large'"
                  class="status-select"
                >
                  <el-option
                    v-for="status in statusOptions"
                    :key="status.value"
                    :label="status.label"
                    :value="status.value"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item class="form-actions">
                <el-button
                  type="primary"
                  :icon="Search"
                  :loading="loading"
                  @click="handleSearch"
                >
                  查询
                </el-button>
                <el-button
                  :icon="Refresh"
                  @click="handleReset"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-transition>
      </el-card>
    </div>

    <!-- Results Section -->
    <div class="results-section">
      <!-- Statistics -->
      <div v-if="statistics" class="statistics-bar">
        <div class="stat-item">
          <span class="stat-label">总计:</span>
          <span class="stat-value">{{ statistics.total }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">金额:</span>
          <span class="stat-value">¥{{ formatCurrency(statistics.totalAmount) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">逾期:</span>
          <span class="stat-value stat-value--danger">{{ statistics.overdueCount }}</span>
        </div>
      </div>

      <!-- Data Table -->
      <ResponsiveTable
        :data="tableData"
        :columns="tableColumns"
        :loading="loading"
        :pagination="true"
        :total="total"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        selectable
        show-index
        primary-column="customerName"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @row-click="handleRowClick"
      >
        <!-- Custom columns -->
        <template #customerName="{ row }">
          <div class="customer-info">
            <div class="customer-name">{{ row.customerName }}</div>
            <div class="customer-phone">{{ row.phone }}</div>
          </div>
        </template>
        
        <template #amount="{ row }">
          <span class="amount-text">¥{{ formatCurrency(row.amount) }}</span>
        </template>
        
        <template #status="{ row }">
          <el-tag
            :type="getStatusType(row.status)"
            size="small"
          >
            {{ row.status }}
          </el-tag>
        </template>
        
        <template #actions="{ row }">
          <div class="action-buttons">
            <el-button
              text
              type="primary"
              size="small"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              text
              type="warning"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
          </div>
        </template>
        
        <!-- Mobile card title -->
        <template #card-title="{ row }">
          <div class="mobile-card-title">
            <span class="customer-name">{{ row.customerName }}</span>
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ row.status }}
            </el-tag>
          </div>
        </template>
      </ResponsiveTable>
    </div>

    <!-- Detail Dialog -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="selectedRow?.customerName || '订单详情'"
      :width="appStore.isMobile ? '95%' : '600px'"
      :fullscreen="appStore.isMobile"
    >
      <OrderDetail
        v-if="selectedRow"
        :order="selectedRow"
        @close="detailDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { ApiClient } from '@/utils/api'
import ResponsiveTable from '@/components/common/ResponsiveTable.vue'
import OrderDetail from '@/components/order/OrderDetail.vue'
import type { Order, QueryParams } from '@/types/api'
import type { TableColumn } from '@/types/table'
import {
  Search,
  Download,
  Refresh,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'

const appStore = useAppStore()

// Reactive data
const loading = ref(false)
const exporting = ref(false)
const expanded = ref(true)
const detailDialogVisible = ref(false)
const selectedRow = ref<Order | null>(null)
const selectedRows = ref<Order[]>([])

const searchForm = reactive({
  dateRange: [] as string[],
  customerName: '',
  phone: '',
  status: ''
})

const tableData = ref<Order[]>([])
const total = ref(0)
const pageSize = ref(20)
const currentPage = ref(1)
const sortBy = ref('')
const sortOrder = ref<'asc' | 'desc'>('desc')

const statistics = ref<{
  total: number
  totalAmount: number
  overdueCount: number
} | null>(null)

// Status options
const statusOptions = [
  { label: '账单日', value: '账单日' },
  { label: '逾期未还', value: '逾期未还' },
  { label: '逾期还款', value: '逾期还款' },
  { label: '提前还款', value: '提前还款' },
  { label: '按时还款', value: '按时还款' }
]

// Table columns
const tableColumns = computed<TableColumn[]>(() => [
  {
    prop: 'orderNumber',
    label: '订单号',
    width: 120,
    sortable: true
  },
  {
    prop: 'customerName',
    label: '客户信息',
    minWidth: 150,
    sortable: true
  },
  {
    prop: 'deviceModel',
    label: '设备型号',
    width: 120,
    mobileVisible: false
  },
  {
    prop: 'amount',
    label: '金额',
    width: 100,
    sortable: true,
    align: 'right'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    sortable: true
  },
  {
    prop: 'billDate',
    label: '账单日期',
    width: 120,
    sortable: true,
    mobileVisible: false,
    formatter: (value: string) => formatDate(value)
  },
  {
    prop: 'dueDate',
    label: '到期日期',
    width: 120,
    sortable: true,
    formatter: (value: string) => formatDate(value)
  }
])

// Lifecycle
onMounted(() => {
  // Set default date range (last 30 days)
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 30)
  
  searchForm.dateRange = [
    formatDate(startDate),
    formatDate(endDate)
  ]
  
  handleSearch()
})

// Methods
async function handleSearch() {
  try {
    loading.value = true
    
    const params: QueryParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value
    }
    
    // Add search filters
    if (searchForm.dateRange.length === 2) {
      params.filters = {
        ...params.filters,
        startDate: searchForm.dateRange[0],
        endDate: searchForm.dateRange[1]
      }
    }
    
    if (searchForm.customerName) {
      params.filters = {
        ...params.filters,
        customerName: searchForm.customerName
      }
    }
    
    if (searchForm.phone) {
      params.filters = {
        ...params.filters,
        phone: searchForm.phone
      }
    }
    
    if (searchForm.status) {
      params.filters = {
        ...params.filters,
        status: searchForm.status
      }
    }
    
    const [dataResponse, statsResponse] = await Promise.all([
      ApiClient.get('/orders', params),
      ApiClient.get('/orders/statistics', params.filters)
    ])
    
    tableData.value = dataResponse.items
    total.value = dataResponse.total
    statistics.value = statsResponse
    
  } catch (error) {
    console.error('Search failed:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

function handleReset() {
  Object.assign(searchForm, {
    dateRange: [],
    customerName: '',
    phone: '',
    status: ''
  })
  
  currentPage.value = 1
  sortBy.value = ''
  sortOrder.value = 'desc'
  
  handleSearch()
}

async function handleExport() {
  try {
    exporting.value = true
    
    const params = {
      format: 'excel',
      filters: {
        startDate: searchForm.dateRange[0],
        endDate: searchForm.dateRange[1],
        customerName: searchForm.customerName,
        phone: searchForm.phone,
        status: searchForm.status
      }
    }
    
    await ApiClient.download('/orders/export', `订单数据_${formatDate(new Date())}.xlsx`, params)
    
    ElMessage.success('导出成功')
    
  } catch (error) {
    console.error('Export failed:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exporting.value = false
  }
}

function handleSelectionChange(selection: Order[]) {
  selectedRows.value = selection
}

function handleSortChange(sort: { prop: string; order: string }) {
  sortBy.value = sort.prop
  sortOrder.value = sort.order === 'ascending' ? 'asc' : 'desc'
  handleSearch()
}

function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  handleSearch()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  handleSearch()
}

function handleRowClick(row: Order) {
  if (appStore.isMobile) {
    handleView(row)
  }
}

function handleView(row: Order) {
  selectedRow.value = row
  detailDialogVisible.value = true
}

function handleEdit(row: Order) {
  // Navigate to edit page or open edit dialog
  ElMessage.info('编辑功能开发中...')
}

// Utility functions
function formatDate(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN')
}

function formatCurrency(amount: number): string {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

function getStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    '账单日': 'info',
    '逾期未还': 'danger',
    '逾期还款': 'warning',
    '提前还款': 'success',
    '按时还款': 'success'
  }
  return typeMap[status] || 'info'
}
</script>

<style lang="scss" scoped>
.data-query-page {
  .search-section {
    margin-bottom: 1.5rem;
    
    .search-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .search-form {
        .el-form-item {
          margin-bottom: 1rem;
          
          @media (max-width: 768px) {
            margin-bottom: 1.5rem;
            
            &.form-actions {
              margin-bottom: 0;
              
              .el-button {
                width: 100%;
                margin-bottom: 0.5rem;
                
                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
        
        .date-picker {
          @media (max-width: 768px) {
            width: 100%;
          }
        }
        
        .status-select {
          @media (max-width: 768px) {
            width: 100%;
          }
        }
      }
    }
  }
  
  .results-section {
    .statistics-bar {
      display: flex;
      gap: 2rem;
      padding: 1rem;
      background: var(--color-bg-secondary);
      border-radius: var(--radius-lg);
      margin-bottom: 1rem;
      
      @media (max-width: 768px) {
        gap: 1rem;
        flex-wrap: wrap;
      }
      
      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        @media (max-width: 768px) {
          flex: 1;
          min-width: 80px;
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
          margin-bottom: 0.25rem;
        }
        
        .stat-value {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--color-text-primary);
          
          &--danger {
            color: var(--color-danger);
          }
        }
      }
    }
  }
  
  .customer-info {
    .customer-name {
      font-weight: 500;
      color: var(--color-text-primary);
    }
    
    .customer-phone {
      font-size: 0.875rem;
      color: var(--color-text-secondary);
      margin-top: 0.25rem;
    }
  }
  
  .amount-text {
    font-weight: 600;
    color: var(--color-success);
  }
  
  .action-buttons {
    display: flex;
    gap: 0.5rem;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0.25rem;
    }
  }
  
  .mobile-card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .customer-name {
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }
}
</style>
