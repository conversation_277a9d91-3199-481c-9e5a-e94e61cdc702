// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// Query types
export interface QueryParams {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}

export interface DateRangeQuery {
  startDate: string
  endDate: string
}

// Order types
export interface Order {
  id: string
  orderNumber: string
  customerName: string
  customerPhone: string
  deviceModel: string
  amount: number
  status: OrderStatus
  billDate: string
  dueDate: string
  paidDate?: string
  remarks?: string
  createdAt: string
  updatedAt: string
}

export type OrderStatus = '账单日' | '逾期未还' | '逾期还款' | '提前还款' | '按时还款'

// Customer types
export interface Customer {
  id: string
  name: string
  phone: string
  email?: string
  address?: string
  totalOrders: number
  totalAmount: number
  overdueCount: number
  lastOrderDate?: string
  createdAt: string
}

export interface CustomerSummary {
  customer: Customer
  orders: Order[]
  statistics: {
    totalAmount: number
    paidAmount: number
    overdueAmount: number
    onTimePayments: number
    latePayments: number
    averagePaymentDays: number
  }
}

// Chart data types
export interface ChartData {
  labels: string[]
  datasets: ChartDataset[]
}

export interface ChartDataset {
  label: string
  data: number[]
  backgroundColor?: string | string[]
  borderColor?: string | string[]
  borderWidth?: number
}

// Export types
export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  filename?: string
  columns?: string[]
  filters?: Record<string, any>
}

// File upload types
export interface UploadFile {
  name: string
  size: number
  type: string
  lastModified: number
  file: File
}

export interface UploadResponse {
  url: string
  filename: string
  size: number
  type: string
}
