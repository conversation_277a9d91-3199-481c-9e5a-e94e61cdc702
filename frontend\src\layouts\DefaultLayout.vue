<template>
  <div class="layout-container h-screen flex overflow-hidden bg-gray-50">
    <!-- Sidebar -->
    <Transition name="slide-x">
      <aside
        v-show="appStore.shouldShowSidebar"
        class="sidebar bg-white shadow-lg flex flex-col"
        :class="sidebarClasses"
      >
        <!-- Sidebar Header -->
        <div class="sidebar-header p-4 border-b border-gray-200">
          <div class="flex items-center justify-center">
            <img
              src="/logo.png"
              alt="Logo"
              class="h-8 w-auto"
              @error="handleLogoError"
            >
            <h1
              v-show="!appStore.sidebarCollapsed || appStore.isMobile"
              class="ml-3 text-xl font-bold text-gray-800"
            >
              太享查询
            </h1>
          </div>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="flex-1 overflow-y-auto py-4">
          <SidebarMenu />
        </nav>
        
        <!-- Sidebar Footer -->
        <div
          v-show="!appStore.sidebarCollapsed || appStore.isMobile"
          class="sidebar-footer p-4 border-t border-gray-200"
        >
          <UserProfile />
        </div>
      </aside>
    </Transition>
    
    <!-- Mobile Overlay -->
    <Transition name="fade">
      <div
        v-if="appStore.isMobile && appStore.mobileMenuOpen"
        class="fixed inset-0 bg-black bg-opacity-50 z-40"
        @click="appStore.closeMobileMenu"
      />
    </Transition>
    
    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top Header -->
      <header class="header bg-white shadow-sm border-b border-gray-200 px-4 py-3">
        <div class="flex items-center justify-between">
          <!-- Left: Menu Toggle & Breadcrumb -->
          <div class="flex items-center space-x-4">
            <button
              class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              @click="appStore.toggleSidebar"
            >
              <el-icon class="text-xl">
                <Menu />
              </el-icon>
            </button>
            
            <Breadcrumb />
          </div>
          
          <!-- Right: Actions -->
          <div class="flex items-center space-x-3">
            <!-- Search -->
            <QuickSearch v-if="!appStore.isSmallScreen" />
            
            <!-- Notifications -->
            <NotificationBell />
            
            <!-- Theme Toggle -->
            <button
              class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              @click="appStore.toggleTheme"
            >
              <el-icon class="text-xl">
                <Sunny v-if="appStore.theme === 'light'" />
                <Moon v-else />
              </el-icon>
            </button>
            
            <!-- User Menu -->
            <UserMenu />
          </div>
        </div>
      </header>
      
      <!-- Page Content -->
      <main class="flex-1 overflow-auto p-4">
        <router-view v-slot="{ Component, route }">
          <Transition
            :name="route.meta.transition || 'fade'"
            mode="out-in"
            appear
          >
            <KeepAlive :include="keepAliveComponents">
              <component :is="Component" :key="route.path" />
            </KeepAlive>
          </Transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import SidebarMenu from '@/components/layout/SidebarMenu.vue'
import UserProfile from '@/components/layout/UserProfile.vue'
import Breadcrumb from '@/components/layout/Breadcrumb.vue'
import QuickSearch from '@/components/layout/QuickSearch.vue'
import NotificationBell from '@/components/layout/NotificationBell.vue'
import UserMenu from '@/components/layout/UserMenu.vue'
import { Menu, Sunny, Moon } from '@element-plus/icons-vue'

const appStore = useAppStore()

// Sidebar classes
const sidebarClasses = computed(() => {
  const classes = ['transition-all', 'duration-300', 'z-50']
  
  if (appStore.isMobile) {
    classes.push('fixed', 'inset-y-0', 'left-0', 'w-64')
  } else {
    classes.push('relative')
    if (appStore.sidebarCollapsed) {
      classes.push('w-16')
    } else {
      classes.push('w-64')
    }
  }
  
  return classes
})

// Keep alive components for better performance
const keepAliveComponents = ['Home', 'DataQuery', 'CustomerSummary']

// Handle logo error
function handleLogoError(event: Event) {
  const img = event.target as HTMLImageElement
  img.src = '/default-logo.png' // Fallback logo
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

.sidebar {
  min-height: 100%;
}

.header {
  height: 64px;
  min-height: 64px;
}

// Transitions
.slide-x-enter-active,
.slide-x-leave-active {
  transition: transform 0.3s ease;
}

.slide-x-enter-from {
  transform: translateX(-100%);
}

.slide-x-leave-to {
  transform: translateX(-100%);
}

// Mobile optimizations
@media (max-width: 768px) {
  .header {
    height: 56px;
    min-height: 56px;
    padding: 0.75rem 1rem;
  }
  
  .sidebar {
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }
}

// Responsive main content
@media (min-width: 769px) {
  .layout-container {
    .sidebar {
      position: relative;
    }
  }
}
</style>
