<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Loading overlay -->
    <Transition name="fade">
      <div
        v-if="appStore.isLoading"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div class="bg-white rounded-lg p-6 flex flex-col items-center">
          <el-icon class="animate-spin text-4xl text-primary-500 mb-4">
            <Loading />
          </el-icon>
          <p class="text-gray-600">{{ appStore.loadingText }}</p>
        </div>
      </div>
    </Transition>

    <!-- Main app content -->
    <router-view v-slot="{ Component, route }">
      <Transition
        :name="route.meta.transition || 'fade'"
        mode="out-in"
        appear
      >
        <component :is="Component" :key="route.path" />
      </Transition>
    </router-view>

    <!-- Global notifications -->
    <Teleport to="body">
      <NotificationContainer />
    </Teleport>

    <!-- Mobile detection and optimization -->
    <MobileDetector />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import NotificationContainer from '@/components/common/NotificationContainer.vue'
import MobileDetector from '@/components/common/MobileDetector.vue'
import { Loading } from '@element-plus/icons-vue'

const appStore = useAppStore()
const userStore = useUserStore()

// Initialize app
onMounted(async () => {
  try {
    appStore.setLoading(true, '正在初始化应用...')
    
    // Initialize user session
    await userStore.initializeSession()
    
    // Initialize app settings
    await appStore.initialize()
    
    // Setup viewport for mobile
    setupViewport()
    
    // Setup global event listeners
    setupGlobalListeners()
    
  } catch (error) {
    console.error('App initialization failed:', error)
    ElMessage.error('应用初始化失败，请刷新页面重试')
  } finally {
    appStore.setLoading(false)
  }
})

onUnmounted(() => {
  cleanupGlobalListeners()
})

// Viewport setup for mobile devices
function setupViewport() {
  const viewport = document.querySelector('meta[name="viewport"]')
  if (viewport) {
    viewport.setAttribute(
      'content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )
  }
}

// Global event listeners
let resizeHandler: () => void
let orientationHandler: () => void

function setupGlobalListeners() {
  // Window resize handler
  resizeHandler = () => {
    appStore.updateViewport()
  }
  window.addEventListener('resize', resizeHandler)
  
  // Orientation change handler for mobile
  orientationHandler = () => {
    setTimeout(() => {
      appStore.updateViewport()
      // Fix viewport height on mobile after orientation change
      document.documentElement.style.setProperty(
        '--vh',
        `${window.innerHeight * 0.01}px`
      )
    }, 100)
  }
  window.addEventListener('orientationchange', orientationHandler)
  
  // Initial viewport height calculation
  document.documentElement.style.setProperty(
    '--vh',
    `${window.innerHeight * 0.01}px`
  )
}

function cleanupGlobalListeners() {
  if (resizeHandler) {
    window.removeEventListener('resize', resizeHandler)
  }
  if (orientationHandler) {
    window.removeEventListener('orientationchange', orientationHandler)
  }
}
</script>

<style lang="scss">
// Global styles
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

// Transitions
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

// Mobile optimizations
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  // Fix for iOS Safari viewport height
  .min-h-screen {
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
  }
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
