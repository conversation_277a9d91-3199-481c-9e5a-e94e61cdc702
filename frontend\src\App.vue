<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <div class="container mx-auto p-4">
      <h1 class="text-3xl font-bold text-center mb-8 text-blue-600">
        太享查询系统
      </h1>

      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">欢迎使用太享查询系统</h2>
        <p class="text-gray-600 mb-4">
          这是一个基于 Vue 3 + TypeScript + Element Plus 的现代化前端应用。
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="bg-blue-50 p-4 rounded-lg">
            <h3 class="font-semibold text-blue-800 mb-2">数据查询</h3>
            <p class="text-sm text-blue-600">快速查询和展示金融数据</p>
          </div>

          <div class="bg-green-50 p-4 rounded-lg">
            <h3 class="font-semibold text-green-800 mb-2">数据汇总</h3>
            <p class="text-sm text-green-600">智能数据分析和汇总</p>
          </div>

          <div class="bg-purple-50 p-4 rounded-lg">
            <h3 class="font-semibold text-purple-800 mb-2">工具集</h3>
            <p class="text-sm text-purple-600">实用的业务工具和功能</p>
          </div>
        </div>

        <div class="mt-6 text-center">
          <el-button type="primary" size="large">
            开始使用
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('太享查询系统前端已启动')
})
</script>

<style lang="scss">
// Global styles
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

// Transitions
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

// Mobile optimizations
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  // Fix for iOS Safari viewport height
  .min-h-screen {
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
  }
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
