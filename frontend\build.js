#!/usr/bin/env node

/**
 * 前端构建脚本
 * 用于构建前端项目并集成到Flask后端
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 配置
const config = {
  frontendDir: __dirname,
  backendStaticDir: path.join(__dirname, '..', 'app', 'static'),
  distDir: path.join(__dirname, '..', 'app', 'static', 'dist'),
  backupDir: path.join(__dirname, '..', 'app', 'static', 'backup'),
  templateDir: path.join(__dirname, '..', 'app', 'templates')
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logStep(step, message) {
  log(`[${step}] ${message}`, 'cyan')
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

// 检查依赖
function checkDependencies() {
  logStep('1/7', '检查依赖...')
  
  try {
    // 检查 Node.js 版本
    const nodeVersion = process.version
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
    
    if (majorVersion < 16) {
      throw new Error(`需要 Node.js 16+，当前版本: ${nodeVersion}`)
    }
    
    logSuccess(`Node.js 版本: ${nodeVersion}`)
    
    // 检查 npm
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim()
    logSuccess(`npm 版本: ${npmVersion}`)
    
    // 检查是否安装了依赖
    if (!fs.existsSync(path.join(config.frontendDir, 'node_modules'))) {
      logWarning('未找到 node_modules，正在安装依赖...')
      execSync('npm install', { 
        cwd: config.frontendDir, 
        stdio: 'inherit' 
      })
    }
    
  } catch (error) {
    logError(`依赖检查失败: ${error.message}`)
    process.exit(1)
  }
}

// 清理旧文件
function cleanup() {
  logStep('2/7', '清理旧文件...')
  
  try {
    // 备份现有文件
    if (fs.existsSync(config.distDir)) {
      if (!fs.existsSync(config.backupDir)) {
        fs.mkdirSync(config.backupDir, { recursive: true })
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const backupPath = path.join(config.backupDir, `dist-${timestamp}`)
      
      fs.renameSync(config.distDir, backupPath)
      logSuccess(`已备份到: ${backupPath}`)
    }
    
    // 创建新的 dist 目录
    fs.mkdirSync(config.distDir, { recursive: true })
    
  } catch (error) {
    logError(`清理失败: ${error.message}`)
    process.exit(1)
  }
}

// 构建前端
function buildFrontend() {
  logStep('3/7', '构建前端项目...')
  
  try {
    // 运行构建命令
    execSync('npm run build', { 
      cwd: config.frontendDir, 
      stdio: 'inherit' 
    })
    
    logSuccess('前端构建完成')
    
  } catch (error) {
    logError(`前端构建失败: ${error.message}`)
    process.exit(1)
  }
}

// 生成资源清单
function generateManifest() {
  logStep('4/7', '生成资源清单...')
  
  try {
    const manifest = {
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '2.0.0',
      files: {}
    }
    
    // 扫描构建文件
    function scanDirectory(dir, basePath = '') {
      const files = fs.readdirSync(dir)
      
      files.forEach(file => {
        const filePath = path.join(dir, file)
        const relativePath = path.join(basePath, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory()) {
          scanDirectory(filePath, relativePath)
        } else {
          manifest.files[relativePath] = {
            size: stat.size,
            modified: stat.mtime.toISOString()
          }
        }
      })
    }
    
    if (fs.existsSync(config.distDir)) {
      scanDirectory(config.distDir)
    }
    
    // 写入清单文件
    const manifestPath = path.join(config.distDir, 'manifest.json')
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
    
    logSuccess(`资源清单已生成: ${Object.keys(manifest.files).length} 个文件`)
    
  } catch (error) {
    logError(`生成资源清单失败: ${error.message}`)
    process.exit(1)
  }
}

// 更新模板文件
function updateTemplates() {
  logStep('5/7', '更新模板文件...')
  
  try {
    // 读取资源清单
    const manifestPath = path.join(config.distDir, 'manifest.json')
    if (!fs.existsSync(manifestPath)) {
      logWarning('未找到资源清单，跳过模板更新')
      return
    }
    
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    
    // 查找主要资源文件
    const assets = {
      css: [],
      js: []
    }
    
    Object.keys(manifest.files).forEach(file => {
      if (file.endsWith('.css')) {
        assets.css.push(file)
      } else if (file.endsWith('.js')) {
        assets.js.push(file)
      }
    })
    
    // 生成资源引用模板
    const assetTemplate = `<!-- Auto-generated asset references -->
<!-- Generated at: ${manifest.timestamp} -->
{% block frontend_assets %}
${assets.css.map(css => `<link rel="stylesheet" href="{{ url_for('static', filename='dist/${css}') }}">`).join('\n')}
${assets.js.map(js => `<script src="{{ url_for('static', filename='dist/${js}') }}"></script>`).join('\n')}
{% endblock %}
`
    
    // 写入资源模板文件
    const assetTemplatePath = path.join(config.templateDir, 'frontend_assets.html')
    fs.writeFileSync(assetTemplatePath, assetTemplate)
    
    logSuccess(`模板文件已更新: ${assets.css.length} CSS, ${assets.js.length} JS`)
    
  } catch (error) {
    logError(`更新模板失败: ${error.message}`)
    process.exit(1)
  }
}

// 验证构建结果
function validateBuild() {
  logStep('6/7', '验证构建结果...')
  
  try {
    // 检查关键文件
    const requiredFiles = ['index.html']
    const missingFiles = []
    
    requiredFiles.forEach(file => {
      const filePath = path.join(config.distDir, file)
      if (!fs.existsSync(filePath)) {
        missingFiles.push(file)
      }
    })
    
    if (missingFiles.length > 0) {
      throw new Error(`缺少关键文件: ${missingFiles.join(', ')}`)
    }
    
    // 检查文件大小
    const manifestPath = path.join(config.distDir, 'manifest.json')
    if (fs.existsSync(manifestPath)) {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
      const totalSize = Object.values(manifest.files).reduce((sum, file) => sum + file.size, 0)
      const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2)
      
      logSuccess(`构建大小: ${totalSizeMB} MB`)
      
      if (totalSize > 50 * 1024 * 1024) { // 50MB
        logWarning('构建文件较大，可能影响加载性能')
      }
    }
    
    logSuccess('构建验证通过')
    
  } catch (error) {
    logError(`构建验证失败: ${error.message}`)
    process.exit(1)
  }
}

// 完成构建
function finalizeBuild() {
  logStep('7/7', '完成构建...')
  
  try {
    // 生成构建信息
    const buildInfo = {
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '2.0.0',
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    }
    
    const buildInfoPath = path.join(config.distDir, 'build-info.json')
    fs.writeFileSync(buildInfoPath, JSON.stringify(buildInfo, null, 2))
    
    // 输出构建摘要
    log('\n' + '='.repeat(50), 'green')
    log('🎉 前端构建完成！', 'green')
    log('='.repeat(50), 'green')
    log(`📁 输出目录: ${config.distDir}`)
    log(`📅 构建时间: ${buildInfo.timestamp}`)
    log(`📦 版本: ${buildInfo.version}`)
    log('\n📋 下一步:')
    log('1. 启动 Flask 开发服务器')
    log('2. 访问 http://localhost:5000 查看效果')
    log('3. 检查浏览器控制台是否有错误')
    log('\n💡 提示:')
    log('- 如果样式异常，请清除浏览器缓存')
    log('- 开发时可使用 npm run dev 进行热重载')
    log('- 生产部署前请运行完整测试')
    
  } catch (error) {
    logError(`完成构建失败: ${error.message}`)
    process.exit(1)
  }
}

// 主函数
function main() {
  log('\n🚀 开始前端构建流程...', 'bright')
  log(`📂 前端目录: ${config.frontendDir}`)
  log(`📂 输出目录: ${config.distDir}`)
  
  try {
    checkDependencies()
    cleanup()
    buildFrontend()
    generateManifest()
    updateTemplates()
    validateBuild()
    finalizeBuild()
    
  } catch (error) {
    logError(`构建流程失败: ${error.message}`)
    process.exit(1)
  }
}

// 处理命令行参数
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  log('前端构建脚本', 'bright')
  log('用法: node build.js [选项]')
  log('\n选项:')
  log('  --help, -h     显示帮助信息')
  log('  --clean        仅清理文件，不构建')
  log('  --dev          开发模式构建')
  log('\n示例:')
  log('  node build.js              # 标准构建')
  log('  node build.js --clean      # 仅清理')
  log('  node build.js --dev        # 开发构建')
  process.exit(0)
}

if (args.includes('--clean')) {
  log('🧹 仅执行清理操作...', 'yellow')
  cleanup()
  logSuccess('清理完成')
  process.exit(0)
}

if (args.includes('--dev')) {
  log('🔧 开发模式构建...', 'yellow')
  process.env.NODE_ENV = 'development'
}

// 执行主函数
main()
