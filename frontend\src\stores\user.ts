import { defineStore } from 'pinia'
import { api } from '@/utils/api'
import type { User, LoginCredentials, UserPermission } from '@/types/user'

export interface UserState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  permissions: UserPermission[]
  lastActivity: number
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    user: null,
    token: null,
    isAuthenticated: false,
    permissions: [],
    lastActivity: Date.now()
  }),
  
  getters: {
    // User info
    userName: (state) => state.user?.name || '',
    userRole: (state) => state.user?.role || '',
    
    // Permission checks
    hasFullPermission: (state) => state.permissions.includes('full'),
    hasStandardPermission: (state) => state.permissions.includes('standard') || state.permissions.includes('full'),
    hasLimitedPermission: (state) => state.permissions.length > 0,
    
    // Session info
    isSessionValid: (state) => {
      if (!state.token || !state.isAuthenticated) return false
      
      // Check if session is expired (24 hours)
      const sessionTimeout = 24 * 60 * 60 * 1000 // 24 hours
      return Date.now() - state.lastActivity < sessionTimeout
    }
  },
  
  actions: {
    // Authentication
    async login(credentials: LoginCredentials) {
      try {
        const response = await api.post('/auth/login', credentials)
        const { user, token, permissions } = response.data
        
        this.setUserData(user, token, permissions)
        this.updateActivity()
        
        return { success: true }
      } catch (error: any) {
        console.error('Login failed:', error)
        return {
          success: false,
          message: error.response?.data?.message || '登录失败，请检查用户名和密码'
        }
      }
    },
    
    async logout() {
      try {
        if (this.token) {
          await api.post('/auth/logout')
        }
      } catch (error) {
        console.error('Logout request failed:', error)
      } finally {
        this.clearUserData()
      }
    },
    
    // Session management
    async initializeSession() {
      const token = this.getStoredToken()
      if (!token) return
      
      try {
        this.token = token
        await this.fetchUserInfo()
        this.isAuthenticated = true
        this.updateActivity()
      } catch (error) {
        console.error('Session initialization failed:', error)
        this.clearUserData()
      }
    },
    
    async restoreSession() {
      if (!this.isSessionValid) {
        throw new Error('Session expired')
      }
      
      try {
        await this.fetchUserInfo()
        this.updateActivity()
      } catch (error) {
        this.clearUserData()
        throw error
      }
    },
    
    async fetchUserInfo() {
      try {
        const response = await api.get('/auth/me')
        const { user, permissions } = response.data
        
        this.user = user
        this.permissions = permissions
        this.isAuthenticated = true
      } catch (error) {
        console.error('Failed to fetch user info:', error)
        throw error
      }
    },
    
    // Permission checks
    hasPermission(permission: UserPermission): boolean {
      return this.permissions.includes(permission)
    },
    
    canAccess(requiredPermission?: UserPermission): boolean {
      if (!requiredPermission) return this.isAuthenticated
      return this.hasPermission(requiredPermission)
    },
    
    // User data management
    setUserData(user: User, token: string, permissions: UserPermission[]) {
      this.user = user
      this.token = token
      this.permissions = permissions
      this.isAuthenticated = true
      
      // Store token
      this.storeToken(token)
    },
    
    clearUserData() {
      this.user = null
      this.token = null
      this.permissions = []
      this.isAuthenticated = false
      
      // Clear stored data
      this.removeStoredToken()
    },
    
    updateActivity() {
      this.lastActivity = Date.now()
    },
    
    // Token storage
    storeToken(token: string) {
      try {
        localStorage.setItem('auth-token', token)
        // Also set as axios default header
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      } catch (error) {
        console.error('Failed to store token:', error)
      }
    },
    
    getStoredToken(): string | null {
      try {
        const token = localStorage.getItem('auth-token')
        if (token) {
          // Set as axios default header
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`
        }
        return token
      } catch (error) {
        console.error('Failed to get stored token:', error)
        return null
      }
    },
    
    removeStoredToken() {
      try {
        localStorage.removeItem('auth-token')
        // Remove from axios headers
        delete api.defaults.headers.common['Authorization']
      } catch (error) {
        console.error('Failed to remove stored token:', error)
      }
    },
    
    // Profile management
    async updateProfile(profileData: Partial<User>) {
      try {
        const response = await api.put('/auth/profile', profileData)
        this.user = { ...this.user!, ...response.data.user }
        
        ElMessage.success('个人信息更新成功')
        return { success: true }
      } catch (error: any) {
        console.error('Profile update failed:', error)
        const message = error.response?.data?.message || '更新失败，请重试'
        ElMessage.error(message)
        return { success: false, message }
      }
    },
    
    async changePassword(oldPassword: string, newPassword: string) {
      try {
        await api.post('/auth/change-password', {
          oldPassword,
          newPassword
        })
        
        ElMessage.success('密码修改成功')
        return { success: true }
      } catch (error: any) {
        console.error('Password change failed:', error)
        const message = error.response?.data?.message || '密码修改失败，请重试'
        ElMessage.error(message)
        return { success: false, message }
      }
    }
  }
})
