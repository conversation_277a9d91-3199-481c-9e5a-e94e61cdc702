<template>
  <div
    class="stat-card"
    :class="[
      `stat-card--${color}`,
      { 'stat-card--loading': loading }
    ]"
    @click="$emit('click')"
  >
    <div class="stat-card__content">
      <div class="stat-card__header">
        <div class="stat-card__icon">
          <el-icon>
            <component :is="iconComponent" />
          </el-icon>
        </div>
        <div v-if="trend" class="stat-card__trend">
          <el-icon
            :class="[
              'trend-icon',
              `trend-icon--${trend.type}`
            ]"
          >
            <ArrowUp v-if="trend.type === 'up'" />
            <ArrowDown v-else />
          </el-icon>
          <span class="trend-value">{{ trend.value }}%</span>
        </div>
      </div>
      
      <div class="stat-card__body">
        <div class="stat-card__value">
          <el-skeleton v-if="loading" :rows="1" animated />
          <CountUp
            v-else
            :end-val="typeof value === 'number' ? value : 0"
            :duration="1.5"
            :options="{ useEasing: true, useGrouping: true }"
          >
            <template #default="{ value: animatedValue }">
              {{ typeof value === 'number' ? animatedValue : value }}
            </template>
          </CountUp>
        </div>
        <div class="stat-card__title">
          {{ title }}
        </div>
      </div>
    </div>
    
    <div class="stat-card__background">
      <div class="background-pattern"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import CountUp from '@/components/common/CountUp.vue'

interface Props {
  title: string
  value: string | number
  icon: string
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  trend?: {
    value: number
    type: 'up' | 'down'
  }
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  color: 'primary',
  loading: false
})

defineEmits<{
  click: []
}>()

// Dynamic icon component
const iconComponent = computed(() => {
  // This would be a mapping of icon names to components
  // For now, we'll use a simple mapping
  const iconMap: Record<string, any> = {
    Calendar: () => import('@element-plus/icons-vue').then(m => m.Calendar),
    Warning: () => import('@element-plus/icons-vue').then(m => m.Warning),
    User: () => import('@element-plus/icons-vue').then(m => m.User),
    Monitor: () => import('@element-plus/icons-vue').then(m => m.Monitor),
    PieChart: () => import('@element-plus/icons-vue').then(m => m.PieChart)
  }
  
  return iconMap[props.icon] || (() => import('@element-plus/icons-vue').then(m => m.QuestionFilled))
})
</script>

<style lang="scss" scoped>
.stat-card {
  position: relative;
  background: var(--color-bg-primary);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-all);
  cursor: pointer;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
  
  &--loading {
    pointer-events: none;
  }
  
  // Color variants
  &--primary {
    border-left: 4px solid var(--color-primary);
    
    .stat-card__icon {
      background: var(--color-primary-lighter);
      color: var(--color-primary);
    }
  }
  
  &--success {
    border-left: 4px solid var(--color-success);
    
    .stat-card__icon {
      background: rgba(103, 194, 58, 0.1);
      color: var(--color-success);
    }
  }
  
  &--warning {
    border-left: 4px solid var(--color-warning);
    
    .stat-card__icon {
      background: rgba(230, 162, 60, 0.1);
      color: var(--color-warning);
    }
  }
  
  &--danger {
    border-left: 4px solid var(--color-danger);
    
    .stat-card__icon {
      background: rgba(245, 108, 108, 0.1);
      color: var(--color-danger);
    }
  }
  
  &--info {
    border-left: 4px solid var(--color-info);
    
    .stat-card__icon {
      background: rgba(144, 147, 153, 0.1);
      color: var(--color-info);
    }
  }
  
  &__content {
    position: relative;
    z-index: 2;
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  &__icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
  }
  
  &__trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    
    .trend-icon {
      font-size: 1rem;
      
      &--up {
        color: var(--color-success);
      }
      
      &--down {
        color: var(--color-danger);
      }
    }
    
    .trend-value {
      color: var(--color-text-secondary);
    }
  }
  
  &__body {
    .stat-card__value {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-primary);
      line-height: 1;
      margin-bottom: 0.5rem;
      
      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }
    
    .stat-card__title {
      font-size: 1rem;
      color: var(--color-text-secondary);
      font-weight: 500;
    }
  }
  
  &__background {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    opacity: 0.05;
    
    .background-pattern {
      width: 100%;
      height: 100%;
      background-image: radial-gradient(circle at 20% 80%, currentColor 2px, transparent 2px),
                        radial-gradient(circle at 80% 20%, currentColor 2px, transparent 2px),
                        radial-gradient(circle at 40% 40%, currentColor 1px, transparent 1px);
      background-size: 30px 30px, 40px 40px, 20px 20px;
      background-position: 0 0, 15px 15px, 10px 10px;
    }
  }
}

// Loading state
.stat-card--loading {
  .stat-card__icon {
    background: var(--color-gray-200);
    color: var(--color-gray-400);
  }
  
  .stat-card__trend {
    opacity: 0.5;
  }
}

// Responsive design
@media (max-width: 768px) {
  .stat-card {
    padding: 1.25rem;
    
    &__icon {
      width: 2.5rem;
      height: 2.5rem;
      font-size: 1.25rem;
    }
    
    &__trend {
      font-size: 0.75rem;
    }
  }
}

// Dark mode support
.dark {
  .stat-card {
    background: var(--color-bg-primary);
    
    &__background {
      opacity: 0.03;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .stat-card {
    border: 2px solid var(--color-border-dark);
    
    &__icon {
      border: 1px solid currentColor;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .stat-card {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}
</style>
