/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActionCard: typeof import('./src/components/home/<USER>')['default']
    CountUp: typeof import('./src/components/common/CountUp.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    MobileDetector: typeof import('./src/components/common/MobileDetector.vue')['default']
    NotificationContainer: typeof import('./src/components/common/NotificationContainer.vue')['default']
    ResponsiveTable: typeof import('./src/components/common/ResponsiveTable.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatCard: typeof import('./src/components/home/<USER>')['default']
  }
}
