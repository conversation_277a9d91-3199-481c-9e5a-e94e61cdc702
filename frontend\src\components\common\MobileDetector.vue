<template>
  <div class="mobile-detector">
    <!-- Mobile-specific optimizations -->
    <div v-if="appStore.isMobile" class="mobile-optimizations">
      <!-- Viewport height fix for iOS -->
      <div class="vh-fix" />
      
      <!-- Touch feedback -->
      <div class="touch-feedback" />
      
      <!-- Safe area support -->
      <div class="safe-area-support" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

let resizeObserver: ResizeObserver | null = null
let orientationChangeTimer: number | null = null

onMounted(() => {
  setupMobileOptimizations()
  setupViewportDetection()
  setupTouchOptimizations()
  setupSafeAreaSupport()
})

onUnmounted(() => {
  cleanup()
})

// Setup mobile-specific optimizations
function setupMobileOptimizations() {
  if (!appStore.isMobile) return
  
  // Disable zoom on input focus (iOS)
  const viewport = document.querySelector('meta[name="viewport"]')
  if (viewport) {
    viewport.setAttribute(
      'content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )
  }
  
  // Prevent pull-to-refresh on mobile
  document.body.style.overscrollBehavior = 'none'
  
  // Add mobile class to body
  document.body.classList.add('mobile-device')
  
  // Setup viewport height fix for iOS
  updateViewportHeight()
}

// Setup viewport detection
function setupViewportDetection() {
  // Use ResizeObserver for better performance
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      appStore.updateViewport()
      updateViewportHeight()
    })
    resizeObserver.observe(document.documentElement)
  } else {
    // Fallback to resize event
    window.addEventListener('resize', handleResize)
  }
  
  // Handle orientation change
  window.addEventListener('orientationchange', handleOrientationChange)
}

// Setup touch optimizations
function setupTouchOptimizations() {
  if (!appStore.isMobile) return
  
  // Improve touch responsiveness
  document.addEventListener('touchstart', () => {}, { passive: true })
  
  // Prevent default touch behaviors on specific elements
  document.addEventListener('touchmove', (e) => {
    // Allow scrolling on scrollable elements
    const target = e.target as Element
    const scrollableParent = target.closest('.scrollable, .el-scrollbar__wrap, .el-table__body-wrapper')
    
    if (!scrollableParent) {
      // Prevent overscroll on non-scrollable areas
      e.preventDefault()
    }
  }, { passive: false })
  
  // Add touch feedback class
  document.body.classList.add('touch-device')
}

// Setup safe area support for notched devices
function setupSafeAreaSupport() {
  // Check if device supports safe area
  const supportsEnv = CSS.supports('padding-top: env(safe-area-inset-top)')
  
  if (supportsEnv) {
    document.documentElement.style.setProperty(
      '--safe-area-top',
      'env(safe-area-inset-top)'
    )
    document.documentElement.style.setProperty(
      '--safe-area-bottom',
      'env(safe-area-inset-bottom)'
    )
    document.documentElement.style.setProperty(
      '--safe-area-left',
      'env(safe-area-inset-left)'
    )
    document.documentElement.style.setProperty(
      '--safe-area-right',
      'env(safe-area-inset-right)'
    )
    
    document.body.classList.add('safe-area-supported')
  } else {
    // Fallback values
    document.documentElement.style.setProperty('--safe-area-top', '0px')
    document.documentElement.style.setProperty('--safe-area-bottom', '0px')
    document.documentElement.style.setProperty('--safe-area-left', '0px')
    document.documentElement.style.setProperty('--safe-area-right', '0px')
  }
}

// Update viewport height for iOS Safari
function updateViewportHeight() {
  const vh = window.innerHeight * 0.01
  document.documentElement.style.setProperty('--vh', `${vh}px`)
  
  // Also update viewport width for horizontal scrolling issues
  const vw = window.innerWidth * 0.01
  document.documentElement.style.setProperty('--vw', `${vw}px`)
}

// Handle resize events
function handleResize() {
  appStore.updateViewport()
  updateViewportHeight()
}

// Handle orientation change
function handleOrientationChange() {
  // Clear previous timer
  if (orientationChangeTimer) {
    clearTimeout(orientationChangeTimer)
  }
  
  // Delay to ensure proper viewport calculation
  orientationChangeTimer = window.setTimeout(() => {
    appStore.updateViewport()
    updateViewportHeight()
    
    // Force layout recalculation
    document.body.style.height = '100.1%'
    setTimeout(() => {
      document.body.style.height = ''
    }, 50)
  }, 100)
}

// Cleanup
function cleanup() {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  
  if (orientationChangeTimer) {
    clearTimeout(orientationChangeTimer)
  }
  
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('orientationchange', handleOrientationChange)
  
  // Remove mobile classes
  document.body.classList.remove('mobile-device', 'touch-device', 'safe-area-supported')
  
  // Reset overscroll behavior
  document.body.style.overscrollBehavior = ''
}
</script>

<style lang="scss">
// Global mobile optimizations
.mobile-device {
  // Improve touch scrolling
  -webkit-overflow-scrolling: touch;
  
  // Prevent text selection on UI elements
  .el-button,
  .el-menu-item,
  .el-tabs__item {
    -webkit-user-select: none;
    user-select: none;
  }
  
  // Improve input focus behavior
  input,
  textarea {
    // Prevent zoom on focus
    font-size: 16px !important;
    
    &:focus {
      // Prevent viewport jumping
      transform: translateZ(0);
    }
  }
}

.touch-device {
  // Add touch feedback to interactive elements
  .el-button,
  .el-card,
  .mobile-card {
    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }
  
  // Improve touch targets
  .el-button {
    min-height: 44px;
    min-width: 44px;
  }
  
  // Better spacing for touch
  .el-menu-item {
    min-height: 48px;
    line-height: 48px;
  }
}

.safe-area-supported {
  // Use safe area insets
  padding-top: var(--safe-area-top);
  padding-bottom: var(--safe-area-bottom);
  padding-left: var(--safe-area-left);
  padding-right: var(--safe-area-right);
}

// Viewport height fix
.vh-fix {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

// Custom scrollbar for mobile
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
}

// Orientation-specific styles
@media (orientation: landscape) and (max-height: 500px) {
  // Compact mode for landscape mobile
  .el-header {
    height: 48px !important;
    min-height: 48px !important;
  }
  
  .sidebar {
    .sidebar-header {
      padding: 0.5rem !important;
    }
  }
}

// High DPI display optimizations
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  // Sharper borders and shadows
  .el-card,
  .el-table,
  .mobile-card {
    border-width: 0.5px;
  }
}

// Reduce motion for users who prefer it
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
