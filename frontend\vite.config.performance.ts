import { defineConfig } from 'vite'
import { resolve } from 'path'

// Performance-optimized Vite configuration
export default defineConfig({
  // Build optimizations
  build: {
    target: 'es2015',
    outDir: '../app/static/dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    
    // Rollup options for advanced optimization
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'element-plus': ['element-plus', '@element-plus/icons-vue'],
          'charts': ['chart.js', 'vue-chartjs', 'chartjs-adapter-date-fns'],
          'utils': ['axios', '@vueuse/core', 'lodash-es', 'dayjs', 'date-fns'],
          
          // Feature chunks
          'auth': ['./src/views/auth/Login.vue', './src/stores/user.ts'],
          'query': ['./src/views/query/DataQuery.vue', './src/views/query/OverdueOrders.vue'],
          'summary': ['./src/views/summary/CustomerSummary.vue', './src/views/summary/DataSummary.vue'],
          'tools': ['./src/views/tools/ContractGenerator.vue', './src/views/tools/ReceiptGenerator.vue']
        },
        
        // Asset naming for better caching
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
          if (facadeModuleId) {
            const name = facadeModuleId.split('/').pop()?.replace('.vue', '') || 'chunk'
            return `js/${name}-[hash].js`
          }
          return 'js/[name]-[hash].js'
        },
        
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || []
          const ext = info[info.length - 1]
          
          if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(assetInfo.name || '')) {
            return 'images/[name]-[hash].[ext]'
          }
          
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
            return 'fonts/[name]-[hash].[ext]'
          }
          
          if (ext === 'css') {
            return 'css/[name]-[hash].[ext]'
          }
          
          return 'assets/[name]-[hash].[ext]'
        }
      },
      
      // External dependencies (if using CDN)
      external: process.env.NODE_ENV === 'production' ? [
        // Uncomment to use CDN versions
        // 'vue',
        // 'vue-router',
        // 'element-plus'
      ] : []
    },
    
    // Terser options for better minification
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2
      },
      mangle: {
        safari10: true
      },
      format: {
        comments: false
      }
    },
    
    // CSS code splitting
    cssCodeSplit: true,
    
    // Asset inlining threshold
    assetsInlineLimit: 4096,
    
    // Chunk size warnings
    chunkSizeWarningLimit: 1000,
    
    // Report compressed size
    reportCompressedSize: true
  },
  
  // Dependency optimization
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'element-plus',
      '@element-plus/icons-vue',
      'axios',
      '@vueuse/core',
      'chart.js',
      'vue-chartjs',
      'date-fns',
      'lodash-es',
      'dayjs'
    ],
    
    // Force optimization of these packages
    force: true
  },
  
  // Server configuration for development
  server: {
    host: '0.0.0.0',
    port: 3000,
    strictPort: true,
    
    // HMR optimization
    hmr: {
      overlay: false
    },
    
    // Proxy configuration
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false
      }
    }
  },
  
  // Preview server configuration
  preview: {
    host: '0.0.0.0',
    port: 4173,
    strictPort: true
  },
  
  // CSS optimization
  css: {
    devSourcemap: false,
    
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/variables.scss" as *;`,
        charset: false
      }
    },
    
    postcss: {
      plugins: [
        // Add PostCSS plugins for optimization
        require('autoprefixer'),
        require('cssnano')({
          preset: ['default', {
            discardComments: { removeAll: true },
            normalizeWhitespace: true,
            colormin: true,
            convertValues: true,
            discardDuplicates: true,
            discardEmpty: true,
            mergeRules: true,
            minifyFontValues: true,
            minifyParams: true,
            minifySelectors: true,
            reduceIdents: false,
            svgo: true,
            uniqueSelectors: true
          }]
        })
      ]
    }
  },
  
  // Asset processing
  assetsInclude: ['**/*.woff', '**/*.woff2', '**/*.ttf', '**/*.eot'],
  
  // Plugin configuration for performance
  plugins: [
    // Add performance-focused plugins here
  ],
  
  // Worker configuration
  worker: {
    format: 'es'
  },
  
  // Experimental features
  experimental: {
    renderBuiltUrl: (filename, { hostType }) => {
      if (hostType === 'js') {
        return { js: `"${filename}"` }
      } else {
        return { relative: true }
      }
    }
  },
  
  // Environment variables
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@assets': resolve(__dirname, 'src/assets'),
      '@types': resolve(__dirname, 'src/types')
    },
    
    // Reduce resolve attempts
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },
  
  // ESBuild configuration
  esbuild: {
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    legalComments: 'none'
  }
})
